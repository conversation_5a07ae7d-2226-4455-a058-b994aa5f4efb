#ifndef ANDROID_GUI_BUFFERAPILOADEREBUG_H
#define ANDROID_GUI_BUFFERAPILOADEREBUG_H

#include <utils/RefBase.h>
#include <gui_debug/BufferQueueDumpAPI.h>

namespace android {
status_t getProcessName(int pid, std::string& name);

// -----------------------------------------------------------------------------
// GuiDebug loader for dl open libgui_debug
class GuiDebugModuleLoader {
public:
    static GuiDebugModuleLoader& getInstance();

    ~GuiDebugModuleLoader();
    BufferQueueDumpAPI* CreateBQDumpInstance();
private:
    GuiDebugModuleLoader();

    // for buffer dump
    void* mBQDumpSoHandle;
    <PERSON><PERSON><PERSON><PERSON><PERSON>ueDumpAPI* (*mCreateBQDumpInstancePtr)();
};

// ----------------------------------------------------------------------------
}; // namespace android
#endif // ANDROID_GUI_BUFFERAPILOADEREBUG_H