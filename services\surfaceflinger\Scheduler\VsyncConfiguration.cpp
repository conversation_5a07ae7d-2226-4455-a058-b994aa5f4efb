/*
 * Copyright 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "VsyncConfiguration.h"

#include <chrono>
#include <cinttypes>
#include <optional>

#include <cutils/properties.h>
#include <log/log.h>

#include "SurfaceFlingerProperties.h"

namespace {

using namespace std::chrono_literals;

std::optional<nsecs_t> getProperty(const char* name) {
    char value[PROPERTY_VALUE_MAX];
    property_get(name, value, "-1");
    if (const int i = atoi(value); i != -1) return i;
    return std::nullopt;
}

#ifdef MTK_DYNAMIC_DURATION
std::optional<nsecs_t> getMtkDuration(const char* name) {
    static bool read = false, sw = false;
    char value[PROPERTY_VALUE_MAX];
    // check switch first
    if (!read) {
        property_get("vendor.debug.sf.dynamic_duration.switch", value, "0");
        if (atoi(value) > 0) sw = true;
        read = true;
    }
    // if switch is off, not allow to get MKT Duration
    if (!sw) return std::nullopt;
    property_get(name, value, "-1");
    if (const int i = atoi(value); i != -1) return i;
    return std::nullopt;
}
#endif

} // namespace

namespace android::scheduler::impl {

VsyncConfiguration::VsyncConfiguration(Fps currentFps) : mRefreshRateFps(currentFps) {}

PhaseOffsets::VsyncConfigSet VsyncConfiguration::getConfigsForRefreshRate(Fps fps) const {
    std::lock_guard lock(mLock);
    return getConfigsForRefreshRateLocked(fps);
}

PhaseOffsets::VsyncConfigSet VsyncConfiguration::getConfigsForRefreshRateLocked(Fps fps) const {
    if (const auto offsets = mOffsetsCache.get(fps)) {
        return offsets->get();
    }

    const auto [it, _] = mOffsetsCache.try_emplace(fps, constructOffsets(fps.getPeriodNsecs()));
    return it->second;
}

void VsyncConfiguration::dump(std::string& result) const {
#ifdef MTK_DYNAMIC_DURATION
    const auto [early, earlyGpu, late, decouple, hwcMinWorkDuration] = getCurrentConfigs();
#else
    const auto [early, earlyGpu, late, hwcMinWorkDuration] = getCurrentConfigs();
#endif
    using base::StringAppendF;
    StringAppendF(&result,
                  "           app phase:    %9" PRId64 " ns\t         SF phase:    %9" PRId64
                  " ns\n"
                  "           app duration: %9lld ns\t         SF duration: %9lld ns\n"
                  "     early app phase:    %9" PRId64 " ns\t   early SF phase:    %9" PRId64
                  " ns\n"
                  "     early app duration: %9lld ns\t   early SF duration: %9lld ns\n"
                  "  GL early app phase:    %9" PRId64 " ns\tGL early SF phase:    %9" PRId64
                  " ns\n"
                  "  GL early app duration: %9lld ns\tGL early SF duration: %9lld ns\n"
#ifdef MTK_DYNAMIC_DURATION
                  "  Decouple app phase:    %9" PRId64 " ns\tDecouple SF phase:    %9" PRId64
                  " ns\n"
                  "  Decouple app duration: %9lld ns\tDecouple SF duration: %9lld ns\n"
#endif
                  "       HWC min duration: %9lld ns\n",
                  late.appOffset, late.sfOffset,

                  late.appWorkDuration.count(), late.sfWorkDuration.count(),

                  early.appOffset, early.sfOffset,

                  early.appWorkDuration.count(), early.sfWorkDuration.count(),

                  earlyGpu.appOffset, earlyGpu.sfOffset,

                  earlyGpu.appWorkDuration.count(), earlyGpu.sfWorkDuration.count(),
#ifdef MTK_DYNAMIC_DURATION
                  decouple.appOffset, decouple.sfOffset,

                  decouple.appWorkDuration.count(), decouple.sfWorkDuration.count(),
#endif
                  hwcMinWorkDuration.count());
}

PhaseOffsets::PhaseOffsets(Fps currentRefreshRate)
      : PhaseOffsets(currentRefreshRate, sysprop::vsync_event_phase_offset_ns(1000000),
                     sysprop::vsync_sf_event_phase_offset_ns(1000000),
                     getProperty("debug.sf.early_phase_offset_ns"),
                     getProperty("debug.sf.early_gl_phase_offset_ns"),
                     getProperty("debug.sf.early_app_phase_offset_ns"),
                     getProperty("debug.sf.early_gl_app_phase_offset_ns"),
                     getProperty("debug.sf.high_fps_late_app_phase_offset_ns").value_or(2000000),
                     getProperty("debug.sf.high_fps_late_sf_phase_offset_ns").value_or(1000000),
                     getProperty("debug.sf.high_fps_early_phase_offset_ns"),
                     getProperty("debug.sf.high_fps_early_gl_phase_offset_ns"),
                     getProperty("debug.sf.high_fps_early_app_phase_offset_ns"),
                     getProperty("debug.sf.high_fps_early_gl_app_phase_offset_ns"),
                     // Below defines the threshold when an offset is considered to be negative,
                     // i.e. targeting for the N+2 vsync instead of N+1. This means that: For offset
                     // < threshold, SF wake up (vsync_duration - offset) before HW vsync. For
                     // offset >= threshold, SF wake up (2 * vsync_duration - offset) before HW
                     // vsync.
                     getProperty("debug.sf.phase_offset_threshold_for_next_vsync_ns")
                             .value_or(std::numeric_limits<nsecs_t>::max()),
#ifdef MTK_SF_CTSONGSI_FIX
                     getProperty("vendor.debug.sf.hwc.min.duration").value_or(23000000)) {}
#else
                     getProperty("debug.sf.hwc.min.duration").value_or(0)) {}
#endif

PhaseOffsets::PhaseOffsets(Fps currentFps, nsecs_t vsyncPhaseOffsetNs, nsecs_t sfVSyncPhaseOffsetNs,
                           std::optional<nsecs_t> earlySfOffsetNs,
                           std::optional<nsecs_t> earlyGpuSfOffsetNs,
                           std::optional<nsecs_t> earlyAppOffsetNs,
                           std::optional<nsecs_t> earlyGpuAppOffsetNs,
                           nsecs_t highFpsVsyncPhaseOffsetNs, nsecs_t highFpsSfVSyncPhaseOffsetNs,
                           std::optional<nsecs_t> highFpsEarlySfOffsetNs,
                           std::optional<nsecs_t> highFpsEarlyGpuSfOffsetNs,
                           std::optional<nsecs_t> highFpsEarlyAppOffsetNs,
                           std::optional<nsecs_t> highFpsEarlyGpuAppOffsetNs,
                           nsecs_t thresholdForNextVsync, nsecs_t hwcMinWorkDuration)
      : VsyncConfiguration(currentFps),
        mVSyncPhaseOffsetNs(vsyncPhaseOffsetNs),
        mSfVSyncPhaseOffsetNs(sfVSyncPhaseOffsetNs),
        mEarlySfOffsetNs(earlySfOffsetNs),
        mEarlyGpuSfOffsetNs(earlyGpuSfOffsetNs),
        mEarlyAppOffsetNs(earlyAppOffsetNs),
        mEarlyGpuAppOffsetNs(earlyGpuAppOffsetNs),
        mHighFpsVSyncPhaseOffsetNs(highFpsVsyncPhaseOffsetNs),
        mHighFpsSfVSyncPhaseOffsetNs(highFpsSfVSyncPhaseOffsetNs),
        mHighFpsEarlySfOffsetNs(highFpsEarlySfOffsetNs),
        mHighFpsEarlyGpuSfOffsetNs(highFpsEarlyGpuSfOffsetNs),
        mHighFpsEarlyAppOffsetNs(highFpsEarlyAppOffsetNs),
        mHighFpsEarlyGpuAppOffsetNs(highFpsEarlyGpuAppOffsetNs),
        mThresholdForNextVsync(thresholdForNextVsync),
        mHwcMinWorkDuration(hwcMinWorkDuration) {}

PhaseOffsets::VsyncConfigSet PhaseOffsets::constructOffsets(nsecs_t vsyncDuration) const {
    if (vsyncDuration < std::chrono::nanoseconds(15ms).count()) {
        return getHighFpsOffsets(vsyncDuration);
    } else {
        return getDefaultOffsets(vsyncDuration);
    }
}

namespace {
std::chrono::nanoseconds sfOffsetToDuration(nsecs_t sfOffset, nsecs_t vsyncDuration) {
    return std::chrono::nanoseconds(vsyncDuration - sfOffset);
}

std::chrono::nanoseconds appOffsetToDuration(nsecs_t appOffset, nsecs_t sfOffset,
                                             nsecs_t vsyncDuration) {
    auto duration = vsyncDuration + (sfOffset - appOffset);
    if (duration < vsyncDuration) {
        duration += vsyncDuration;
    }

    return std::chrono::nanoseconds(duration);
}
} // namespace

PhaseOffsets::VsyncConfigSet PhaseOffsets::getDefaultOffsets(nsecs_t vsyncDuration) const {
    const auto earlySfOffset =
            mEarlySfOffsetNs.value_or(mSfVSyncPhaseOffsetNs) < mThresholdForNextVsync

            ? mEarlySfOffsetNs.value_or(mSfVSyncPhaseOffsetNs)
            : mEarlySfOffsetNs.value_or(mSfVSyncPhaseOffsetNs) - vsyncDuration;
    const auto earlyAppOffset = mEarlyAppOffsetNs.value_or(mVSyncPhaseOffsetNs);
    const auto earlyGpuSfOffset =
            mEarlyGpuSfOffsetNs.value_or(mSfVSyncPhaseOffsetNs) < mThresholdForNextVsync

            ? mEarlyGpuSfOffsetNs.value_or(mSfVSyncPhaseOffsetNs)
            : mEarlyGpuSfOffsetNs.value_or(mSfVSyncPhaseOffsetNs) - vsyncDuration;
    const auto earlyGpuAppOffset = mEarlyGpuAppOffsetNs.value_or(mVSyncPhaseOffsetNs);
    const auto lateSfOffset = mSfVSyncPhaseOffsetNs < mThresholdForNextVsync
            ? mSfVSyncPhaseOffsetNs
            : mSfVSyncPhaseOffsetNs - vsyncDuration;
    const auto lateAppOffset = mVSyncPhaseOffsetNs;

    return {
            .early = {.sfOffset = earlySfOffset,
                      .appOffset = earlyAppOffset,
                      .sfWorkDuration = sfOffsetToDuration(earlySfOffset, vsyncDuration),
                      .appWorkDuration =
                              appOffsetToDuration(earlyAppOffset, earlySfOffset, vsyncDuration)},
            .earlyGpu = {.sfOffset = earlyGpuSfOffset,
                         .appOffset = earlyGpuAppOffset,
                         .sfWorkDuration = sfOffsetToDuration(earlyGpuSfOffset, vsyncDuration),
                         .appWorkDuration = appOffsetToDuration(earlyGpuAppOffset, earlyGpuSfOffset,
                                                                vsyncDuration)},
            .late = {.sfOffset = lateSfOffset,
                     .appOffset = lateAppOffset,
                     .sfWorkDuration = sfOffsetToDuration(lateSfOffset, vsyncDuration),
                     .appWorkDuration =
                             appOffsetToDuration(lateAppOffset, lateSfOffset, vsyncDuration)},
            .hwcMinWorkDuration = std::chrono::nanoseconds(mHwcMinWorkDuration),
    };
}

PhaseOffsets::VsyncConfigSet PhaseOffsets::getHighFpsOffsets(nsecs_t vsyncDuration) const {
    const auto earlySfOffset =
            mHighFpsEarlySfOffsetNs.value_or(mHighFpsSfVSyncPhaseOffsetNs) < mThresholdForNextVsync
            ? mHighFpsEarlySfOffsetNs.value_or(mHighFpsSfVSyncPhaseOffsetNs)
            : mHighFpsEarlySfOffsetNs.value_or(mHighFpsSfVSyncPhaseOffsetNs) - vsyncDuration;
    const auto earlyAppOffset = mHighFpsEarlyAppOffsetNs.value_or(mHighFpsVSyncPhaseOffsetNs);
    const auto earlyGpuSfOffset = mHighFpsEarlyGpuSfOffsetNs.value_or(
                                          mHighFpsSfVSyncPhaseOffsetNs) < mThresholdForNextVsync

            ? mHighFpsEarlyGpuSfOffsetNs.value_or(mHighFpsSfVSyncPhaseOffsetNs)
            : mHighFpsEarlyGpuSfOffsetNs.value_or(mHighFpsSfVSyncPhaseOffsetNs) - vsyncDuration;
    const auto earlyGpuAppOffset = mHighFpsEarlyGpuAppOffsetNs.value_or(mHighFpsVSyncPhaseOffsetNs);
    const auto lateSfOffset = mHighFpsSfVSyncPhaseOffsetNs < mThresholdForNextVsync
            ? mHighFpsSfVSyncPhaseOffsetNs
            : mHighFpsSfVSyncPhaseOffsetNs - vsyncDuration;
    const auto lateAppOffset = mHighFpsVSyncPhaseOffsetNs;

    return {
            .early =
                    {
                            .sfOffset = earlySfOffset,
                            .appOffset = earlyAppOffset,
                            .sfWorkDuration = sfOffsetToDuration(earlySfOffset, vsyncDuration),
                            .appWorkDuration = appOffsetToDuration(earlyAppOffset, earlySfOffset,
                                                                   vsyncDuration),
                    },
            .earlyGpu =
                    {
                            .sfOffset = earlyGpuSfOffset,
                            .appOffset = earlyGpuAppOffset,
                            .sfWorkDuration = sfOffsetToDuration(earlyGpuSfOffset, vsyncDuration),
                            .appWorkDuration = appOffsetToDuration(earlyGpuAppOffset,
                                                                   earlyGpuSfOffset, vsyncDuration),
                    },
            .late =
                    {
                            .sfOffset = lateSfOffset,
                            .appOffset = lateAppOffset,
                            .sfWorkDuration = sfOffsetToDuration(lateSfOffset, vsyncDuration),
                            .appWorkDuration =
                                    appOffsetToDuration(lateAppOffset, lateSfOffset, vsyncDuration),
                    },
            .hwcMinWorkDuration = std::chrono::nanoseconds(mHwcMinWorkDuration),
    };
}

static void validateSysprops() {
    const auto validatePropertyBool = [](const char* prop) {
        LOG_ALWAYS_FATAL_IF(!property_get_bool(prop, false), "%s is false", prop);
    };

    validatePropertyBool("debug.sf.use_phase_offsets_as_durations");

    LOG_ALWAYS_FATAL_IF(sysprop::vsync_event_phase_offset_ns(-1) != -1,
                        "ro.surface_flinger.vsync_event_phase_offset_ns is set but expecting "
                        "duration");

    LOG_ALWAYS_FATAL_IF(sysprop::vsync_sf_event_phase_offset_ns(-1) != -1,
                        "ro.surface_flinger.vsync_sf_event_phase_offset_ns is set but expecting "
                        "duration");

    const auto validateProperty = [](const char* prop) {
        LOG_ALWAYS_FATAL_IF(getProperty(prop).has_value(),
                            "%s is set to %" PRId64 " but expecting duration", prop,
                            getProperty(prop).value_or(-1));
    };

    validateProperty("debug.sf.early_phase_offset_ns");
    validateProperty("debug.sf.early_gl_phase_offset_ns");
    validateProperty("debug.sf.early_app_phase_offset_ns");
    validateProperty("debug.sf.early_gl_app_phase_offset_ns");
    validateProperty("debug.sf.high_fps_late_app_phase_offset_ns");
    validateProperty("debug.sf.high_fps_late_sf_phase_offset_ns");
    validateProperty("debug.sf.high_fps_early_phase_offset_ns");
    validateProperty("debug.sf.high_fps_early_gl_phase_offset_ns");
    validateProperty("debug.sf.high_fps_early_app_phase_offset_ns");
    validateProperty("debug.sf.high_fps_early_gl_app_phase_offset_ns");
}

namespace {
nsecs_t sfDurationToOffset(std::chrono::nanoseconds sfDuration, nsecs_t vsyncDuration) {
    return vsyncDuration - sfDuration.count() % vsyncDuration;
}

nsecs_t appDurationToOffset(std::chrono::nanoseconds appDuration,
                            std::chrono::nanoseconds sfDuration, nsecs_t vsyncDuration) {
    return vsyncDuration - (appDuration + sfDuration).count() % vsyncDuration;
}
} // namespace

WorkDuration::VsyncConfigSet WorkDuration::constructOffsets(nsecs_t vsyncDuration) const {
    const auto sfDurationFixup = [vsyncDuration](nsecs_t duration) {
        return duration == -1 ? std::chrono::nanoseconds(vsyncDuration) - 1ms
                              : std::chrono::nanoseconds(duration);
    };

    const auto appDurationFixup = [vsyncDuration](nsecs_t duration) {
        return duration == -1 ? std::chrono::nanoseconds(vsyncDuration)
                              : std::chrono::nanoseconds(duration);
    };

#ifdef MTK_DYNAMIC_DURATION
    std::string refresh_rate;
    if (vsyncDuration >= 16666666) refresh_rate = ".60";
    else if (vsyncDuration == 11111111) refresh_rate = ".90";
    else if (vsyncDuration <= 8888888) refresh_rate = ".120";

    const std::string prop_sfEarly
        = "vendor.debug.sf.dynamic_duration.sf.early" + refresh_rate;
    const std::string prop_appEarly
        = "vendor.debug.sf.dynamic_duration.app.early" + refresh_rate;
    const std::string prop_sfEarlyGl
        = "vendor.debug.sf.dynamic_duration.sf.earlyGl" + refresh_rate;
    const std::string prop_appEarlyGl
        = "vendor.debug.sf.dynamic_duration.app.earlyGl" + refresh_rate;
    const std::string prop_sfLate
        = "vendor.debug.sf.dynamic_duration.sf.late" + refresh_rate;
    const std::string prop_appLate
        = "vendor.debug.sf.dynamic_duration.app.late" + refresh_rate;

    const auto sfEarlyDuration
        = sfDurationFixup(getMtkDuration(prop_sfEarly.c_str()).value_or(mSfEarlyDuration));
    const auto appEarlyDuration
        = appDurationFixup(getMtkDuration(prop_appEarly.c_str()).value_or(mAppEarlyDuration));
    const auto sfEarlyGpuDuration
        = sfDurationFixup(getMtkDuration(prop_sfEarlyGl.c_str()).value_or(mSfEarlyGpuDuration));
    const auto appEarlyGpuDuration
        = appDurationFixup(getMtkDuration(prop_appEarlyGl.c_str()).value_or(mAppEarlyGpuDuration));
    const auto sfDuration
        = sfDurationFixup(getMtkDuration(prop_sfLate.c_str()).value_or(mSfDuration));
    const auto appDuration
        = appDurationFixup(getMtkDuration(prop_appLate.c_str()).value_or(mAppDuration));
    ALOGI("WorkDuration::constructOffsets, vsync=%" PRId64 ", app=%" PRId64", sf=%" PRId64,
            vsyncDuration, static_cast<int64_t>(appDuration.count()), static_cast<int64_t>(sfDuration.count()));
#else
    const auto sfEarlyDuration = sfDurationFixup(mSfEarlyDuration);
    const auto appEarlyDuration = appDurationFixup(mAppEarlyDuration);
    const auto sfEarlyGpuDuration = sfDurationFixup(mSfEarlyGpuDuration);
    const auto appEarlyGpuDuration = appDurationFixup(mAppEarlyGpuDuration);
    const auto sfDuration = sfDurationFixup(mSfDuration);
    const auto appDuration = appDurationFixup(mAppDuration);
#endif

#ifdef MTK_DYNAMIC_DURATION
    const auto sfDecoupleDuration = sfDurationFixup(mSfDecoupleDuration);
    const auto appDecoupleDuration = appDurationFixup(mAppDecoupleDuration);
#endif

    return {
            .early =
                    {

                            .sfOffset = sfEarlyDuration.count() < vsyncDuration
                                    ? sfDurationToOffset(sfEarlyDuration, vsyncDuration)
                                    : sfDurationToOffset(sfEarlyDuration, vsyncDuration) -
                                            vsyncDuration,

                            .appOffset = appDurationToOffset(appEarlyDuration, sfEarlyDuration,
                                                             vsyncDuration),

                            .sfWorkDuration = sfEarlyDuration,
                            .appWorkDuration = appEarlyDuration,
                    },
            .earlyGpu =
                    {

                            .sfOffset = sfEarlyGpuDuration.count() < vsyncDuration

                                    ? sfDurationToOffset(sfEarlyGpuDuration, vsyncDuration)
                                    : sfDurationToOffset(sfEarlyGpuDuration, vsyncDuration) -
                                            vsyncDuration,

                            .appOffset = appDurationToOffset(appEarlyGpuDuration,
                                                             sfEarlyGpuDuration, vsyncDuration),
                            .sfWorkDuration = sfEarlyGpuDuration,
                            .appWorkDuration = appEarlyGpuDuration,
                    },
            .late =
                    {

                            .sfOffset = sfDuration.count() < vsyncDuration

                                    ? sfDurationToOffset(sfDuration, vsyncDuration)
                                    : sfDurationToOffset(sfDuration, vsyncDuration) - vsyncDuration,

                            .appOffset =
                                    appDurationToOffset(appDuration, sfDuration, vsyncDuration),

                            .sfWorkDuration = sfDuration,
                            .appWorkDuration = appDuration,
                    },
#ifdef MTK_DYNAMIC_DURATION
             .decouple = {
                            .sfOffset = sfDecoupleDuration.count() < vsyncDuration

                                    ? sfDurationToOffset(sfDecoupleDuration, vsyncDuration)
                                    : sfDurationToOffset(sfDecoupleDuration, vsyncDuration) - vsyncDuration,

                            .appOffset =
                                    appDurationToOffset(appDecoupleDuration, sfDecoupleDuration, vsyncDuration),

                            .sfWorkDuration = sfDecoupleDuration,
                            .appWorkDuration = appDecoupleDuration,
                    },
#endif
            .hwcMinWorkDuration = std::chrono::nanoseconds(mHwcMinWorkDuration),
    };
}

WorkDuration::WorkDuration(Fps currentRefreshRate)
      : WorkDuration(currentRefreshRate, getProperty("debug.sf.late.sf.duration").value_or(-1),
                     getProperty("debug.sf.late.app.duration").value_or(-1),
                     getProperty("debug.sf.early.sf.duration").value_or(mSfDuration),
                     getProperty("debug.sf.early.app.duration").value_or(mAppDuration),
                     getProperty("debug.sf.earlyGl.sf.duration").value_or(mSfDuration),
                     getProperty("debug.sf.earlyGl.app.duration").value_or(mAppDuration),
#ifdef MTK_DYNAMIC_DURATION
                     getProperty("debug.sf.late.sf.duration").value_or(-1),
                     getProperty("debug.sf.late.app.duration").value_or(-1),
#endif
#ifdef MTK_SF_CTSONGSI_FIX
                     getProperty("vendor.debug.sf.hwc.min.duration").value_or(23000000)) {
#else
                     getProperty("debug.sf.hwc.min.duration").value_or(0)) {
#endif
    validateSysprops();
}

WorkDuration::WorkDuration(Fps currentRefreshRate, nsecs_t sfDuration, nsecs_t appDuration,
                           nsecs_t sfEarlyDuration, nsecs_t appEarlyDuration,
                           nsecs_t sfEarlyGpuDuration, nsecs_t appEarlyGpuDuration,
                           nsecs_t hwcMinWorkDuration)
      : VsyncConfiguration(currentRefreshRate),
        mSfDuration(sfDuration),
        mAppDuration(appDuration),
        mSfEarlyDuration(sfEarlyDuration),
        mAppEarlyDuration(appEarlyDuration),
        mSfEarlyGpuDuration(sfEarlyGpuDuration),
        mAppEarlyGpuDuration(appEarlyGpuDuration),
#ifdef MTK_DYNAMIC_DURATION
        mSfDecoupleDuration(sfDuration),
        mAppDecoupleDuration(appDuration),
#endif
        mHwcMinWorkDuration(hwcMinWorkDuration) {}

#ifdef MTK_DYNAMIC_DURATION
WorkDuration::WorkDuration(Fps currentRefreshRate, nsecs_t sfDuration, nsecs_t appDuration,
                           nsecs_t sfEarlyDuration, nsecs_t appEarlyDuration,
                           nsecs_t sfEarlyGpuDuration, nsecs_t appEarlyGpuDuration,
                           nsecs_t sfDecoupleDuration, nsecs_t appDecoupleDuration,
                           nsecs_t hwcMinWorkDuration)
      : VsyncConfiguration(currentRefreshRate),
        mSfDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.sf.late").value_or(sfDuration)),
        mAppDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.app.late").value_or(appDuration)),
        mSfEarlyDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.sf.early").value_or(sfEarlyDuration)),
        mAppEarlyDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.app.early").value_or(appEarlyDuration)),
        mSfEarlyGpuDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.sf.earlyGl").value_or(sfEarlyGpuDuration)),
        mAppEarlyGpuDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.app.earlyGl").value_or(appEarlyGpuDuration)),
        mSfDecoupleDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.sf.decouple").value_or(sfDecoupleDuration)),
        mAppDecoupleDuration(getMtkDuration("vendor.debug.sf.dynamic_duration.app.decouple").value_or(appDecoupleDuration)),
        mHwcMinWorkDuration(hwcMinWorkDuration) {
    if (getProperty("vendor.debug.sf.dynamic_duration.switch").value_or(0) > 0) {
        LOG_ALWAYS_FATAL_IF(
            (mSfDecoupleDuration != -1 &&
            mSfDecoupleDuration != getProperty("debug.sf.late.sf.duration").value_or(-1)),
            "aosp late and mtk decouple duration must be the same");
    }
}
#endif


} // namespace android::scheduler::impl
