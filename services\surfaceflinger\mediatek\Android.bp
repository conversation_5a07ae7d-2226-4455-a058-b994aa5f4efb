bootstrap_go_package {
    name:"soong-surfaceflinger-mediatek",
    pkgPath:"android/soong/surfaceflinger/mediatek",
    deps:[
        "soong-android",
        "soong-cc",
    ],
    srcs:[
        "mtksurfaceflinger.go",
    ],
    pluginFor:["soong_build"],
}

mtk_surfaceflinger_config_defaults {
    name: "mtk_surfaceflinger_config",
}

soong_config_module_type_import {
    from: "device/mediatek/build/config/Android.bp",
    module_types: ["mtk_cc_defaults"],
}

mtk_cc_defaults {
    name: "libgpud_sys_dependencies",
    soong_config_variables: {
        cf_vendor: {
            conditions_default: {
                cflags: [
                    "-DMTK_HDR_DISPLAY_SUPPORT",
                    "-DMTK_GPUD_SUPPORT",
                ],
                shared_libs: [
                    "libgpud_sys",
                ],
            },
        },
    }
}

cc_defaults {
    name: "mtk_surfaceflinger_defaults",
    defaults: [
        "libgpud_sys_dependencies",
        "mtk_surfaceflinger_config",
    ],
    cflags: [
        "-DMTK_AOSP_DISPLAY_BUGFIX",
        "-DMTK_SF_CPU_POLICY",
        "-DMTK_SF_CPU_POLICY_FOR_LEGACY",
        "-DMTK_SF_DEBUG_SUPPORT",
        "-DMTK_SF_GUI_DEBUG_SUPPORT",
        "-DMTK_SF_HWC_VDS_SUPPORT",
        "-DMTK_DISABLE_COLOR_TRANSFORM_FOR_SECONDARY_DISPLAYS",
        "-DMTK_DISPLAY_DECOMPRESSION",
        "-DMTK_MIRROR_SUPPORT",
        "-DMTK_DISPLAY_DEJITTER",
        "-DMTK_VDS_HDCP",
        "-DMTK_ATRACE_PRESENT_FENCE",
        "-DMTK_SF_HINT_DISPLAY_INFO",
        "-DMTK_SF_EXTEND_BACKPRESSURE",
        "-DMTK_DYNAMIC_DURATION",
        "-DMTK_COMPOSER_EXT",
        "-DMTK_SF_MSYNC",
        "-DMTK_IN_DISPLAY_FINGERPRINT",
        "-DMTK_VSYNC_HINT_SUPPORT",
        "-DMTK_SF_WATCHDOG_SUPPORT",
        "-DMTK_SF_PQ_MANAGEMENT",
        "-DMTK_SF_NOTIFY_EXPECTED_PRESENT_TIME",
        "-DMTK_SF_PERF_API",
        "-DMTK_SF_SMART_COMPOSITION",
        "-DMTK_SF_MSYNC_3",
        "-DMTK_SF_AIDL",
        "-DMTK_SF_SCHEDULE_DELAY",
        "-DMTK_SF_UPDATE_DISPLAY_CAP",
        "-DMTK_SF_PEEK_DISPLAY_BRIGHTNESS_SUPPORT",
        "-DMTK_SF_CTSONGSI_FIX",
        "-DMTK_VDS_EXPECTED_PRESENT_TIME",
        "-DMTK_SF_KICK_IDLE",
        "-DMTK_SF_HWC_REPAINT_SUPPORT",
    ],
    cppflags: [
    ],
    srcs: [
    ],
    static_libs: [
    ],
    shared_libs: [
        "libcomposer_ext",
        "libbinder_ndk",
        "vendor.mediatek.framework.mtksf_ext-V1-ndk",
    ],
    header_libs: [
        "libgem_headers",
        "libgralloc_metadata_sys_headers",
    ],
}

cc_defaults {
    name: "mtk_libsurfaceflinger_defaults",
    cflags: [
    ],
    cppflags: [
    ],
    srcs: [
    ],
    static_libs: [
    ],
    shared_libs: [
        "libgralloc_extra_sys",
        "libsf_cpupolicy",
    ],
    header_libs: [
    ],
}

filegroup {
    name: "mtk_libsurfaceflinger_sources",
    srcs: [
        "SurfaceFlinger.cpp",
        "BufferDumpAPILoader.cpp",
        "BufferQueueDebug.cpp",
        "BufferLayer.cpp",
        "DispDeJitterHelper.cpp",
        ":mtk_libsfcpupolicy_sources",
        "CoreDump.cpp",
        "MtkDebugAPI.cpp",
        "SFDebugAPILoader.cpp",
        "MSync/MSyncTester.cpp",
        "MSync/MSyncSfApi.cpp",
        "DisplayHardware/PowerAdvisor.cpp",
        "DisplayHardware/PowerHalWrapper.cpp",
        "Scheduler/VSyncHinter.cpp",
        "SFWatchDogAPILoader.cpp",
        "SFPerfAPILoader.cpp",
        "aidl/MtkSF_ext.cpp",
    ],
}

filegroup {
    name: "mtk_libsfcpupolicy_sources",
    srcs: [
        "SfCpuPolicyAdapter/SfCpuPolicyAdapter.cpp",
        "SfCpuPolicyAdapter/SfLegacyCpuPolicyAdapter.cpp",
    ],
}

cc_library {
    name: "libsf_mtk_property",
    cflags: [
        "-DMTK_SF_DEBUG_SUPPORT",
    ],
    srcs: [
        "SFProperty.cpp",
    ],
    export_include_dirs: ["."],
    shared_libs: [
        "liblog",
        "libutils",
        "libcutils",
    ],
    header_libs: [
    ],
}
