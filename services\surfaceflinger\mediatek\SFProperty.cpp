/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2018. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */
#include "SFProperty.h"

#include <cutils/properties.h>
#include <log/log.h>
#include <sstream>

namespace android {

#ifdef MTK_SF_DEBUG_SUPPORT
SFProperty& SFProperty::getInstance() {
    static SFProperty gInstance;
    return gInstance;
}

PropertiesState* SFProperty::getPropState()
{
    return &sPropertiesState;
}

void SFProperty::setMTKProperties() {
    std::string result;
    setMTKProperties(result);
    ALOGD("%s", result.c_str());
}

void SFProperty::setMTKProperties(std::string &result) {
    std::ostringstream ss;
    char value[PROPERTY_VALUE_MAX];

    ss << "[" << __func__ << "]\n";
    ss << "========================================================================\n";

    property_get("debug.sf.showupdates", value, "0");
    int debugFlashDelay = atoi(value);
    ss << "    debug.sf.showupdates (mDebugFlashDelay): " << debugFlashDelay << std::endl;

    property_get("debug.sf.ddms", value, "0");
    int debugDDMS = atoi(value);
    ss << "    debug.sf.ddms (mDebugDDMS): " << debugDDMS << std::endl;

    if (0 != debugDDMS) {
        // FIX-ME:  Why remove DdmConnection.cpp from Android.mk
        //DdmConnection::start(getServiceName());
        ALOGI("DDMS debugging not supported");
    }

    ss << "[MediaTek SF]\n";

    property_get("vendor.debug.sf.slowmotion", value, "0");
    sPropertiesState.mDelayTime = std::chrono::milliseconds(atoi(value));
    ss << "    vendor.debug.sf.slowmotion (mDelayTime ms): " << sPropertiesState.mDelayTime.count()
            << std::endl;

    property_get("vendor.debug.sf.downgrade_vsync", value, "0");
    sPropertiesState.mDowngradeVsync = static_cast<nsecs_t>(atoi(value));
    ss << "    vendor.debug.sf.downgrade_vsync (min vsync ns): " << sPropertiesState.mDowngradeVsync
                << std::endl;

    property_get("vendor.debug.sf.rpt_fbthint", value, "0");
    sPropertiesState.mFBTHitHint = atoi(value);
    ss << "    vendor.debug.sf.rpt_fbthint (mFBTHitHint): "
            << sPropertiesState.mFBTHitHint << std::endl;

    property_get("vendor.debug.sf.log_transaction", value, "0");
    sPropertiesState.mLogTransaction = atoi(value);
    ss << "    vendor.debug.sf.log_transaction (mLogTransaction): "
            << sPropertiesState.mLogTransaction << std::endl;

    // for internal screen composition update
    property_get("vendor.debug.sf.log_repaint", value, "0");
    sPropertiesState.mLogRepaint = atoi(value);
    ss << "    vendor.debug.sf.log_repaint (mLogRepaint): " << sPropertiesState.mLogRepaint
            << std::endl;

    // debug utils
    property_get("vendor.debug.sf.line_g3d", value, "0");
    sPropertiesState.mLineG3D = atoi(value);
    ss << "    vendor.debug.sf.line_g3d (mLineG3D): " << sPropertiesState.mLineG3D << std::endl;

    property_get("vendor.debug.sf.dump_ss", value, "0");
    sPropertiesState.mDumpScreenShot = atoi(value);
    ss << "    vendor.debug.sf.dump_ss (mDumpScreenShot): " << sPropertiesState.mDumpScreenShot
            << std::endl;

    ss << "[MediaTek GUI]\n";
    // just get and print, real switches should be in libgui

    property_get("vendor.debug.bq.dump", value, "NULL");
    ss << "    vendor.debug.bq.dump: " << value << std::endl;

    property_get("vendor.debug.bq.line", value, "0");
    ss << "    vendor.debug.bq.line: " << value << std::endl;

    ss << "    *** dynamic modification ***\n";

    property_get("vendor.debug.bq.line_p", value, "0");
    ss << "        vendor.debug.bq.line_p (set fixed index): " << value << std::endl;

    property_get("vendor.debug.bq.line_g", value, "0");
    ss << "        vendor.debug.bq.line_g (set drawing grid, ex: 16:1 / 1:16): " << value
            << std::endl;

    property_get("vendor.debug.bq.line_c", value, "0");
    ss << "        vendor.debug.bq.line_c (set drawing color): " << value << std::endl;

    ss << "========================================================================\n\n";
    result.append(ss.str());
}

#endif

}; // namespace android
