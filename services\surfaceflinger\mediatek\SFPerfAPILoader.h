#ifdef MTK_SF_PERF_API
#pragma once

#include "sf_perf/PerfHelper.h"

namespace android {
class PerfHelper;
typedef PerfHelper* (*createPerfHelperFn)();
typedef void (*DestroyPerfHelperFn)(PerfHelper*);
typedef int (*SetUclampMinFn)(PerfHelper*, pid_t, int);
typedef int (*SetTaskUclampFn)(PerfHelper*, int, int);
typedef int (*AddThreadtoCgroupFn)(PerfHelper*, pid_t);

class SFPerfAPILoader {
public:

    static SFPerfAPILoader& getInstance();

    ~SFPerfAPILoader();
    int perfUclampMinRequest(pid_t pid, int uClampMin);
    int perfTaskUclampRequest(int uClampMin, int uClampMax);
    int perfAddThreadRequest(pid_t pid);

private:
    SFPerfAPILoader();

    // handle of dlopen latency library
    void* m_so_handle;
    PerfHelper* mPerfHelper;
    DestroyPerfHelperFn mFnDestroyPerfHelper;
    SetUclampMinFn mFnSetUclampMin;
    SetTaskUclampFn mFnSetTaskUclamp;
    AddThreadtoCgroupFn mFnAddThreadtoCgroup;
};
} // namespace android
#endif
