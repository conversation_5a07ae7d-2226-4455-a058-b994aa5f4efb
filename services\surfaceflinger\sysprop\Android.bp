package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_native_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_native_license"],
}

sysprop_library {
    name: "SurfaceFlingerProperties",
    srcs: ["*.sysprop"],
    api_packages: ["android.sysprop"],
    property_owner: "Platform",
}
