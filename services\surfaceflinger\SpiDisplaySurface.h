#pragma once

#include <compositionengine/DisplaySurface.h>
#include <gui/IGraphicBufferProducer.h>
#include <gui/IGraphicBufferConsumer.h>
#include <gui/BufferQueue.h>
#include <ui/GraphicBuffer.h>
#include <ui/Fence.h>
#include <utils/String8.h>
#include <utils/Errors.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>

namespace android {

class SpiDisplaySurface : public compositionengine::DisplaySurface {
public:
    SpiDisplaySurface(uint32_t width, uint32_t height);
    virtual ~SpiDisplaySurface();

    // DisplaySurface interface
    virtual status_t beginFrame(bool mustRecompose) override;
    virtual status_t prepareFrame(CompositionType compositionType) override;
    virtual status_t advanceFrame() override;
    virtual void onFrameCommitted() override;
    virtual void dumpAsString(String8& result) const override;
    virtual void resizeBuffers(const ui::Size& size) override;
    virtual const sp<Fence>& getClientTargetAcquireFence() const override;
    
    sp<IGraphicBufferProducer> getProducer() const { return mProducer; }

private:
    uint32_t mWidth;
    uint32_t mHeight;
    sp<IGraphicBufferConsumer> mConsumer;
    sp<IGraphicBufferProducer> mProducer;
    sp<Fence> mClientTargetAcquireFence;
    
    void sendFrameToSpi(const sp<GraphicBuffer>& buffer);
    status_t setupBufferQueue();
};

} // namespace android