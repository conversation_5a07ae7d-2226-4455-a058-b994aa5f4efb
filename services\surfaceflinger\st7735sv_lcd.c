#include <linux/module.h>
#include <linux/spi/spi.h>
#include <linux/of.h>
#include <linux/of_device.h>
#include <linux/delay.h>
#include "spi_tiny_lcd.h"
#include <linux/gpio.h>
#include <linux/of_gpio.h>
#include <linux/regulator/consumer.h>
#include <linux/proc_fs.h>
#include <linux/seq_file.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/slab.h>
#include <linux/uaccess.h>
#include <linux/fs.h>
#include <linux/vmalloc.h>
#include <linux/fb.h>
#include "show.h"

#define ST7735SV_PROC_NODE "agn_spi_lcd"
#define ST7735SV_PROC_CTRL "lcd_control"
#define ST7735SV_PROC_BRIGHTNESS "brightness"
#define ST7735SV_IOC_MAGIC 's'
#define ST7735SV_IOC_SUSPEND _IO(ST7735SV_IOC_MAGIC, 1)
#define ST7735SV_IOC_RESUME  _IO(ST7735SV_IOC_MAGIC, 2)
#define ST7735SV_IOC_TEST_SHOW   _IO(ST7735SV_IOC_MAGIC, 3)
#define ST7735SV_IOC_GET_STATUS  _IOR(ST7735SV_IOC_MAGIC, 4, int)
#define ST7735SV_IOC_SET_BRIGHTNESS _IOW(ST7735SV_IOC_MAGIC, 5, int)
#define ST7735SV_IOC_GET_BRIGHTNESS _IOR(ST7735SV_IOC_MAGIC, 6, int)

// Mutex protection
#define ST7735SV_LOCK()   mutex_lock(&st7735sv_mutex_spi_write)
#define ST7735SV_UNLOCK() mutex_unlock(&st7735sv_mutex_spi_write)

// 10. Resolution and parameter definitions
#define ST7735SV_WIDTH  128
#define ST7735SV_HEIGHT 128
#define ST7735SV_X_END  0x80
#define ST7735SV_Y_END  0x80
// Buffer size to match full screen data (128*128*2=32768)
#define ST7735SV_BUFSIZ ((ST7735SV_WIDTH) * (ST7735SV_HEIGHT) * 2) + 1
// #define ST7735SV_BUFSIZ 40000
// #define ST7735SV_BUFSIZ 1024

// Power state synchronization
static int st7735sv_power_state = 0; // 0: off, 1: on
static int st7735sv_brightness = 0; // Default brightness level (0-7) for BCT3220ELA
static int st7735sv_suspend_status = 0; // 0: backlight on, 1: backlight off (suspended)

static struct stl_data st7735sv_data;
static struct stl_data *st7735sv_gp_data = &st7735sv_data;
static unsigned st7735sv_bufsiz = ST7735SV_BUFSIZ;
static dev_t st7735sv_dev_no;
static dev_t fb_dev;// Framebuffer 设备号
static struct cdev *st7735sv_cdev;
static struct class *st7735sv_class;
static struct class *st7735sv_fb_class; // Framebuffer 设备类 
static struct device *st7735sv_device;
static struct mutex st7735sv_mutex_spi_write;
static struct proc_dir_entry *st7735sv_proc_dir = NULL;

// Forward declarations for DT parse, file node, proc, etc.
static int st7735sv_parse_dt(struct stl_data *pointer);
static int st7735sv_init_file_node(void);
static int st7735sv_proc_ctrl_read(struct seq_file *m, void *v);
static ssize_t st7735sv_proc_ctrl_write(struct file *filp, const char *buff, size_t size, loff_t *pos);
static int st7735sv_proc_ctrl_open(struct inode *inode, struct file *file);
static int st7735sv_proc_brightness_read(struct seq_file *m, void *v);
static ssize_t st7735sv_proc_brightness_write(struct file *filp, const char *buff, size_t size, loff_t *pos);
static int st7735sv_proc_brightness_open(struct inode *inode, struct file *file);

static const struct file_operations st7735sv_proc_ctrl_fops = {
    .owner = THIS_MODULE,
    .read = seq_read,
    .open = st7735sv_proc_ctrl_open,
    .write = st7735sv_proc_ctrl_write,
};

static const struct file_operations st7735sv_proc_brightness_fops = {
    .owner = THIS_MODULE,
    .read = seq_read,
    .open = st7735sv_proc_brightness_open,
    .write = st7735sv_proc_brightness_write,
};

// Function prototypes
static int st7735sv_probe(struct spi_device *spi);
static int st7735sv_remove(struct spi_device *spi);
static int __init st7735sv_init(void);
static void __exit st7735sv_exit(void);

static void st7735sv_set_backlight(u8 step);
static void st7735sv_led_ctrl(int on) {
    if (on) {
        // Apply current brightness setting when turning on backlight
        st7735sv_set_backlight(st7735sv_brightness);
    } else {
        // Turn off backlight
        gpio_direction_output(st7735sv_gp_data->en_led_gpio, 0);
    }
}

// SPI communication functions
// These functions use spidev_sync_write to send data/command to the LCD controller.
// The DC GPIO must be set to indicate command (0) or data (1).

/**
 * Send a single command byte to the LCD controller.
 */
static int spi_lcd_send_cmd(uint8_t cmd) {
    int ret;
    struct stl_data *spidev = st7735sv_gp_data;
    // ST7735SV_LOCK();
    ret = gpio_direction_output(spidev->dc_ctrl_gpio, 0); // DC=0 for command
    if (ret) { ST7735SV_UNLOCK(); return ret; }
    struct spi_transfer t = {
        .tx_buf = &cmd,
        .len = 1,
        .speed_hz = spidev->speed_hz,
    };
    struct spi_message m;
    spi_message_init(&m);
    spi_message_add_tail(&t, &m);
    ret = spidev_sync(spidev, &m);
    // ST7735SV_UNLOCK();
    return ret;
}

/**
 * Send a single data byte to the LCD controller.
 */
static int spi_lcd_send_data(uint8_t data) {
    int ret;
    struct stl_data *spidev = st7735sv_gp_data;
    // ST7735SV_LOCK();
    ret = gpio_direction_output(spidev->dc_ctrl_gpio, 1); // DC=1 for data
    if (ret) { ST7735SV_UNLOCK(); return ret; }
    struct spi_transfer t = {
        .tx_buf = &data,
        .len = 1,
        .speed_hz = spidev->speed_hz,
    };
    struct spi_message m;
    spi_message_init(&m);
    spi_message_add_tail(&t, &m);
    ret = spidev_sync(spidev, &m);
    // ST7735SV_UNLOCK();
    return ret;
}

/**
 * Send a buffer of data to the LCD controller.
 */
static int spi_lcd_send_data_buf(uint8_t *buf, unsigned int len) {
    int ret;
    if (!buf || len == 0) {
        LOG_ERR("Invalid buffer or length!\n");
        return -EINVAL;
    }
    
    struct stl_data *spidev = st7735sv_gp_data;
    if (!spidev) {
        LOG_ERR("spidev is NULL!\n");
        return -ENODEV;
    }
    
    // 确保不超过缓冲区大小
    if (len > ST7735SV_BUFSIZ) {
        LOG_ERR("Truncating len %u to %u\n", len, ST7735SV_BUFSIZ);
        len = ST7735SV_BUFSIZ;
    }
	
    // ST7735SV_LOCK();
    if (!spidev->tx_buffer) {
        LOG_ERR(" tx_buffer is NULL! buf=%p, len=%zu\n", buf, len);
        return -ENOMEM;
    }
    if (len > ST7735SV_BUFSIZ) {
        LOG_ERR(" send_data_buf len(%zu) > bufsiz(%u), truncating. buf=%p\n", len, ST7735SV_BUFSIZ, buf);
        len = ST7735SV_BUFSIZ;
    } else {
        pr_info(" send_data_buf buf=%p, len=%zu\n", buf, len);
    }
    ret = gpio_direction_output(spidev->dc_ctrl_gpio, 1); // DC=1 for data
    if (ret) { 
        // ST7735SV_UNLOCK(); 
        return ret; 
    }
    memcpy(spidev->tx_buffer, buf, len);
    struct spi_transfer t = {
        .tx_buf = spidev->tx_buffer,
        .len = len,
        .speed_hz = spidev->speed_hz,
    };
    struct spi_message m;
    spi_message_init(&m);
    spi_message_add_tail(&t, &m);
    ret = spidev_sync(spidev, &m);
    // pr_info(" 222 ret=%d\n", ret);
    // ST7735SV_UNLOCK();
    return ret;
}

/**
 * Parse device tree and request all necessary GPIOs for ST7735SV LCD.
 */
static int st7735sv_parse_dt(struct stl_data *pdata)
{
    int ret = 0;
    struct device_node *np = pdata->spi->dev.of_node;

    pr_info(" parse_dt entry\n");
    // reset gpio
    pdata->reset_gpio = of_get_named_gpio(np, "tiny_lcd,reset-gpio", 0);
    if (pdata->reset_gpio < 0) {
        LOG_ERR(" Unable to get reset_gpio\n");
    }
    // dc_ctrl gpio
    pdata->dc_ctrl_gpio = of_get_named_gpio(np, "tiny_lcd,dc_ctrl-gpio", 0);
    if (pdata->dc_ctrl_gpio < 0) {
        LOG_ERR(" Unable to get dc_ctrl_gpio\n");
    }
    // en_1v8 gpio
    pdata->en_1v8_gpio = of_get_named_gpio(np, "tiny_lcd,en_1v8-gpio", 0);
    if (pdata->en_1v8_gpio < 0) {
        LOG_ERR(" Unable to get en_1v8_gpio\n");
    }
    // en_2v8 gpio
    pdata->en_2v8_gpio = of_get_named_gpio(np, "tiny_lcd,en_2v8-gpio", 0);
    if (pdata->en_2v8_gpio < 0) {
        LOG_ERR(" Unable to get en_2v8_gpio\n");
    }
    // en_led gpio
    pdata->en_led_gpio = of_get_named_gpio(np, "tiny_lcd,en_led-gpio", 0);
    if (pdata->en_led_gpio < 0) {
        LOG_ERR(" Unable to get en_led_gpio\n");
    }

    // request and set direction for all GPIOs
    if (gpio_is_valid(pdata->reset_gpio)) {
        ret = gpio_request(pdata->reset_gpio, "st7735sv_reset_gpio");
        if (ret) {
            LOG_ERR(" reset gpio request failed\n");
            goto err_gpio;
        }
        ret = gpio_direction_output(pdata->reset_gpio, 0);
        if (ret) {
            LOG_ERR(" set_direction for reset gpio failed\n");
            goto err_gpio;
        }
    }
    if (gpio_is_valid(pdata->dc_ctrl_gpio)) {
        ret = gpio_request(pdata->dc_ctrl_gpio, "st7735sv_dc_ctrl_gpio");
        if (ret) {
            LOG_ERR(" dc_ctrl gpio request failed\n");
            goto err_gpio;
        }
        ret = gpio_direction_output(pdata->dc_ctrl_gpio, 0);
        if (ret) {
            LOG_ERR(" set_direction for dc_ctrl gpio failed\n");
            goto err_gpio;
        }
    }
    if (gpio_is_valid(pdata->en_1v8_gpio)) {
        ret = gpio_request(pdata->en_1v8_gpio, "st7735sv_en_1v8_gpio");
        if (ret) {
            LOG_ERR(" en_1v8 gpio request failed\n");
            goto err_gpio;
        }
        ret = gpio_direction_output(pdata->en_1v8_gpio, 0);
        if (ret) {
            LOG_ERR(" set_direction for en_1v8 gpio failed\n");
            goto err_gpio;
        }
    }
    if (gpio_is_valid(pdata->en_2v8_gpio)) {
        ret = gpio_request(pdata->en_2v8_gpio, "st7735sv_en_2v8_gpio");
        if (ret) {
            LOG_ERR(" en_2v8 gpio request failed\n");
            goto err_gpio;
        }
        ret = gpio_direction_output(pdata->en_2v8_gpio, 0);
        if (ret) {
            LOG_ERR(" set_direction for en_2v8 gpio failed\n");
            goto err_gpio;
        }
    }
    if (gpio_is_valid(pdata->en_led_gpio)) {
        ret = gpio_request(pdata->en_led_gpio, "st7735sv_en_led_gpio");
        if (ret) {
            LOG_ERR(" en_led gpio request failed\n");
            goto err_gpio;
        }
        ret = gpio_direction_output(pdata->en_led_gpio, 0);
        if (ret) {
            LOG_ERR(" set_direction for en_led gpio failed\n");
            goto err_gpio;
        }
    }
    pr_info(" parse_dt successful\n");
    return 0;
err_gpio:
    LOG_ERR(" parse_dt error: %d\n", ret);
    return ret;
}

/**
 * Power on the LCD (enable power and backlight)
 */
static void st7735sv_power_on(void) {
    if (st7735sv_power_state) {
        pr_info(" power already on\n");
        return;
    }
    gpio_direction_output(st7735sv_gp_data->en_1v8_gpio, 1);
    gpio_direction_output(st7735sv_gp_data->en_2v8_gpio, 1);
    mdelay(10);
    st7735sv_power_state = 1;
    pr_info(" power on\n");
}

/**
 * Power off the LCD (disable power and backlight)
 */
static void st7735sv_power_off(void) {
    if (!st7735sv_power_state) {
        pr_info(" power already off\n");
        return;
    }
    st7735sv_led_ctrl(0);
    gpio_direction_output(st7735sv_gp_data->en_2v8_gpio, 0);
    gpio_direction_output(st7735sv_gp_data->en_1v8_gpio, 0);
    mdelay(10);
    st7735sv_power_state = 0;
    pr_info(" power off\n");
}

/**
 * Set backlight brightness using BCT3220ELA controller
 * @param step: Brightness level (0-7)  max 0, min 7
 */
static void st7735sv_set_backlight(u8 step)
{
    u8 i;
    
    if(step > 7) {
        LOG_ERR("backlight step setting err! (max 7)\n");
        return;
    }
    
    // Save the brightness level directly (0-7)
    st7735sv_brightness = step;
    
    // Turn off backlight
    gpio_direction_output(st7735sv_gp_data->en_led_gpio, 0);
    mdelay(5);
    
    // Turn on backlight
    gpio_direction_output(st7735sv_gp_data->en_led_gpio, 1);
    udelay(35);
    
    // Generate pulses according to the step
    for(i = 0; i < step; i++) {
        gpio_direction_output(st7735sv_gp_data->en_led_gpio, 0);
        udelay(5);
        gpio_direction_output(st7735sv_gp_data->en_led_gpio, 1);
        udelay(5);
    }
    
    mdelay(1);
    pr_info("Set backlight to step %d\n", step);
}

/**
 * Hardware reset the LCD
 */
static void st7735sv_hw_reset(void) {
    gpio_direction_output(st7735sv_gp_data->reset_gpio, 1);
    mdelay(10);
    gpio_direction_output(st7735sv_gp_data->reset_gpio, 0);
    mdelay(10);
    gpio_direction_output(st7735sv_gp_data->reset_gpio, 1);
    mdelay(120);
}

/**
 * ST7735SV initialization sequence 
 */
static void st7735sv_send_init_setting(void) {    
    spi_lcd_send_cmd(0x11);     //Sleep out

	mdelay(120);                //Delay 120ms
	
	spi_lcd_send_cmd(0xB1);     
	spi_lcd_send_data(0x05);   
	spi_lcd_send_data(0x3C);   
	spi_lcd_send_data(0x3C);   
	
	spi_lcd_send_cmd(0xB2);     
	spi_lcd_send_data(0x05);   
	spi_lcd_send_data(0x3C);   
	spi_lcd_send_data(0x3C);   
	
	spi_lcd_send_cmd(0xB3);     
	spi_lcd_send_data(0x05);   
	spi_lcd_send_data(0x3C);   
	spi_lcd_send_data(0x3C);   
	spi_lcd_send_data(0x05);   
	spi_lcd_send_data(0x3C);   
	spi_lcd_send_data(0x3C);   
	
	spi_lcd_send_cmd(0xB4);     //Dot inversion
	spi_lcd_send_data(0x03);   
	
	spi_lcd_send_cmd(0xC0);     
	spi_lcd_send_data(0xA2);   
	spi_lcd_send_data(0x02);   
	spi_lcd_send_data(0x84);   
	
	spi_lcd_send_cmd(0xC1);     
	spi_lcd_send_data(0xC8);   
	
	spi_lcd_send_cmd(0xC2);     
	spi_lcd_send_data(0x0D);   
	spi_lcd_send_data(0x00);   
	
	spi_lcd_send_cmd(0xC3);     
	spi_lcd_send_data(0x8D);   
	spi_lcd_send_data(0x2A);   
	
	spi_lcd_send_cmd(0xC4);     
	spi_lcd_send_data(0x8D);   
	spi_lcd_send_data(0xEE);   
	
	spi_lcd_send_cmd(0xC5);     
	spi_lcd_send_data(0x16);   
	
	spi_lcd_send_cmd(0xE0);     
	spi_lcd_send_data(0x15);
	spi_lcd_send_data(0x0D);
	spi_lcd_send_data(0x04);
	spi_lcd_send_data(0x04);
	spi_lcd_send_data(0x0C);
	spi_lcd_send_data(0x06);
	spi_lcd_send_data(0x00);
	spi_lcd_send_data(0x05);
	spi_lcd_send_data(0x07);
	spi_lcd_send_data(0x0B);
	spi_lcd_send_data(0x0C);
	spi_lcd_send_data(0x35);
	spi_lcd_send_data(0x10);
	spi_lcd_send_data(0x13);
	spi_lcd_send_data(0x05);
	spi_lcd_send_data(0x10);
	
	spi_lcd_send_cmd(0xE1);     
	spi_lcd_send_data(0x13);
	spi_lcd_send_data(0x10);
	spi_lcd_send_data(0x02);
	spi_lcd_send_data(0x07);
	spi_lcd_send_data(0x09);
	spi_lcd_send_data(0x03);
	spi_lcd_send_data(0x00);
	spi_lcd_send_data(0x06);
	spi_lcd_send_data(0x08);
	spi_lcd_send_data(0x0D);
	spi_lcd_send_data(0x0D);
	spi_lcd_send_data(0x3A);
	spi_lcd_send_data(0x10);
	spi_lcd_send_data(0x10);
	spi_lcd_send_data(0x06);
	spi_lcd_send_data(0x10);
	
	//spi_lcd_send_cmd(0x35);     
	//spi_lcd_send_data(0x00);   
	
	spi_lcd_send_cmd(0x3A);     //65k mode
	spi_lcd_send_data(0x05);   
	
	spi_lcd_send_cmd(0x36);     
	spi_lcd_send_data(0xC8);   //C8 BGR  C0 RGB
	
	spi_lcd_send_cmd(0x20);  // 0x21    
	
	spi_lcd_send_cmd(0x29);     //Display on
}

/**
 * Set display window (column address xs~xe, row address ys+0x20~ye+0x20)
 */
static void st7735sv_set_address_window(uint8_t xs, uint8_t ys, uint8_t xe, uint8_t ye) {
    spi_lcd_send_cmd(0x2A); // Column address set
    spi_lcd_send_data(xs >> 8); spi_lcd_send_data(xs & 0xFF);
    spi_lcd_send_data(xe >> 8); spi_lcd_send_data(xe & 0xFF);
    spi_lcd_send_cmd(0x2B); // Row address set
    spi_lcd_send_data(ys >> 8); spi_lcd_send_data((ys+0x20) & 0xFF);
    spi_lcd_send_data(ye >> 8); spi_lcd_send_data((ye+0x20) & 0xFF);
    spi_lcd_send_cmd(0x2C); // Memory write
}

#define RED_H   0xF8    // RGB565 red high byte
#define RED_L   0x00    // RGB565 red low byte
#define GRE_H   0x07    // RGB565 green high byte
#define GRE_L   0xE0    // RGB565 green low byte
#define WHI_H   0xFF    // RGB565 white high byte
#define WHI_L   0xFF    // RGB565 white low byte

void show_welcome(void)
{
    uint32_t pixel_count = 0x80 * 0x80 * 2;
    uint32_t welcome_size;
    uint8_t *data_buf;
    int i, j = 0;

    // Get the actual size of the showWelcome array
    welcome_size = sizeof(showWelcome) / sizeof(showWelcome[0]);
    
    // Check if the array size is sufficient
    if (welcome_size < pixel_count) {
        LOG_ERR("showWelcome array size is insufficient, expected %u, actual %u\n", 
                pixel_count, welcome_size);
        pixel_count = welcome_size; // Use the smaller value to prevent overflow
    }

    st7735sv_set_address_window(0, 0, 128, 128);

    // Allocate a temporary buffer, size is pixel count * 2 (2 bytes per pixel)
    data_buf = kmalloc(pixel_count, GFP_KERNEL);
    if (!data_buf) {
        LOG_ERR("malloc error!\n");
        return;
    }
   
    for (i = 0; i < pixel_count; i += 2) {
        data_buf[j++] = showWelcome[i+1];        // Low byte
        data_buf[j++] = showWelcome[i] ; // High byte
    }

    // Send all data at once
    ST7735SV_LOCK();
    spi_lcd_send_data_buf(data_buf, pixel_count);
    ST7735SV_UNLOCK();

    // Free the temporary buffer
    kfree(data_buf);
}
void show_welcome_16bit(void)
{
    uint32_t pixel_count = 0x80 * 0x80;
    uint32_t welcome_size;
    uint8_t *data_buf;
    int i, j = 0;

    // Get the actual size of the showWelcome array
    welcome_size = sizeof(showWelcome) / sizeof(showWelcome[0]);
    
    // Check if the array size is sufficient
    if (welcome_size < pixel_count) {
        LOG_ERR("showWelcome array size is insufficient, expected %u, actual %u\n", 
                pixel_count, welcome_size);
        pixel_count = welcome_size; // Use the smaller value to prevent overflow
    }

    st7735sv_set_address_window(0, 0, 128, 128);

    // Allocate a temporary buffer, size is pixel count * 2 (2 bytes per pixel)
    data_buf = kmalloc(pixel_count * 2, GFP_KERNEL);
    if (!data_buf) {
        LOG_ERR("malloc error!\n");
        return;
    }

    // Convert uint16_t array to byte stream format
    for (i = 0; i < pixel_count; i++) {
        data_buf[j++] = showWelcome[i] & 0xFF;        // Low byte
        data_buf[j++] = (showWelcome[i] >> 8) & 0xFF; // High byte
    }

    // Send all data at once
    ST7735SV_LOCK();
    spi_lcd_send_data_buf(data_buf, pixel_count * 2);
    ST7735SV_UNLOCK();

    // Free the temporary buffer
    kfree(data_buf);
}

void show_picture_white(void)
{
    uint32_t pixel_count = 0x81 * 0x81;

    st7735sv_set_address_window(0, 0, 128, 128);

    // Write white pixel data (RGB565)
    for (uint32_t i = 0; i < pixel_count; i++) {
        spi_lcd_send_data(WHI_H);              // High byte
        spi_lcd_send_data(WHI_L);              // Low byte 
    }
}

static uint8_t color_buf[128 * 128 * 2];  // 128x128，RGB565，每像素2字节

static void fill_color(uint16_t color)
{
    for (int i = 0; i < sizeof(color_buf); i += 2) {
        color_buf[i] = (uint8_t)(color >> 8);      // 高位
        color_buf[i + 1] = (uint8_t)(color & 0xFF); // 低位
    }
}

void show_rgb_fade_effect(void)
{
    st7735sv_set_address_window(0, 0, 128, 128);

    // 红色渐变
    for (int r = 0; r <= 0x1F; r += 2) {  // RGB565: 红5位
        uint16_t color = (r << 11);       // R<<11
        fill_color(color);
        spi_lcd_send_data_buf(color_buf, sizeof(color_buf));
        msleep(100);
    }

    // 绿色渐变
    for (int g = 0; g <= 0x3F; g += 4) {  // 绿6位
        uint16_t color = (g << 5);       // G<<5
        fill_color(color);
        spi_lcd_send_data_buf(color_buf, sizeof(color_buf));
        msleep(100);
    }

    // 蓝色渐变
    for (int b = 0; b <= 0x1F; b += 2) {  // 蓝5位
        uint16_t color = b;              // B在低位
        fill_color(color);
        spi_lcd_send_data_buf(color_buf, sizeof(color_buf));
        msleep(100);
    }
}

/**
 * Initialize the LCD (power on, reset, send init sequence)
 */
static void st7735sv_lcd_init(void) {
    st7735sv_power_on();
    st7735sv_hw_reset();
    st7735sv_send_init_setting();
    show_welcome();
    mdelay(100);
    st7735sv_led_ctrl(1);
    st7735sv_suspend_status = 0; // Set backlight status to on after initialization
}

/**
 * Suspend (only turn off backlight)
 */
static void st7735sv_lcd_suspend(void) {
    if (st7735sv_suspend_status) {
        pr_info(" LCD already suspended\n");
        return;
    }
    // Only turn off backlight, don't power off
    st7735sv_led_ctrl(0);
    spi_lcd_send_cmd(0x28); // Display off
    mdelay(50);
    spi_lcd_send_cmd(0x10); // Display off
    mdelay(120);

    st7735sv_suspend_status = 1;
    pr_info(" LCD suspended (backlight off)\n");
}

/**
 * Resume (only turn on backlight)
 */
static void st7735sv_lcd_resume(void) {
    if (!st7735sv_suspend_status) {
        pr_info(" LCD already resumed\n");
        return;
    }
    spi_lcd_send_cmd(0x11); // 
    mdelay(50);
    spi_lcd_send_cmd(0x29); // Display on
    mdelay(120);
    // Only turn on backlight, don't re-initialize
    st7735sv_led_ctrl(1);
    st7735sv_suspend_status = 0;
    pr_info(" LCD resumed (backlight on)\n");
}

// V1 write function
static ssize_t st7735sv_write(struct file *filp, const char __user *buf, size_t count, loff_t *f_pos) {
    ssize_t ret = 0;
    struct stl_data *spidev = st7735sv_gp_data;
    uint8_t *temp_buffer = NULL;
    int i, j;

    LOG_ERR("lzl_debug 00 count=%d\n", count);
    if (count == 0)
        return 0;
        
    if (count > ST7735SV_BUFSIZ) {
        LOG_ERR(" write size exceeds buffer size, truncating\n");
        count = ST7735SV_BUFSIZ;
    }

    // check if count is even (each pixel is 2 bytes)
    if (count % 2 != 0) {
        LOG_ERR(" count must be even (each pixel is 2 bytes)\n");
        return -EINVAL;
    }
    
    // LOG_ERR("lzl_debug 11 count=%d \n", count);
    
    // malloc temp_buffer
    temp_buffer = kmalloc(count, GFP_KERNEL);
    if (!temp_buffer) {
        LOG_ERR(" failed to allocate temp buffer\n");
        return -ENOMEM;
    }
    ST7735SV_LOCK();
	
	if (!spidev || !spidev->tx_buffer) {
        LOG_ERR("Invalid spidev or tx_buffer\n");
        ret = -ENOMEM;
        goto unlock;
    }
    
    temp_buffer = kmalloc(count, GFP_KERNEL);
    if (!temp_buffer) {
        LOG_ERR("kmalloc failed\n");
        ret = -ENOMEM;
        goto unlock;
    }
    
    if (copy_from_user(temp_buffer, buf, count)) {
        LOG_ERR("copy_from_user failed\n");
        ret = -EFAULT;
        goto free_temp;
    }
	
    // copy data from user space to temp_buffer
    if (copy_from_user(temp_buffer, buf, count)) {
        LOG_ERR("lzl_debug 22 copy_from_user failed\n");
        kfree(temp_buffer);
        ST7735SV_UNLOCK();
        return -EFAULT;
    }
    
    // execute byte order conversion (low, high) -> (high, low)
    for (i = 0, j = 0; i < count; i += 2, j += 2) {
        spidev->tx_buffer[j] = temp_buffer[i+1];    // high
        spidev->tx_buffer[j+1] = temp_buffer[i];    // low
    }
    
    // free temp_buffer
    kfree(temp_buffer);
    
    // set flush window
    st7735sv_set_address_window(0, 0, ST7735SV_X_END, ST7735SV_Y_END);
    // LOG_ERR("lzl_debug 33\n");
    
    // send buf
    ret = spi_lcd_send_data_buf(spidev->tx_buffer, count);
    if (ret < 0) {
        LOG_ERR("SPI write failed: %d\n", ret);
    }

free_temp:
    kfree(temp_buffer);
unlock:
    ST7735SV_UNLOCK();
    return (ret < 0) ? ret : count;
}

/**
 * Ioctl: Control suspend, resume, show white screen, etc.
 */
static long st7735sv_ioctl(struct file *filp, unsigned int cmd, unsigned long arg) {
    int status, brightness;
    void __user *argp = (void __user *)arg;

    switch (cmd) {
        case ST7735SV_IOC_SUSPEND:
            st7735sv_lcd_suspend();
            break;
        case ST7735SV_IOC_RESUME:
            st7735sv_lcd_resume();
            break;
        case ST7735SV_IOC_TEST_SHOW:
            show_rgb_fade_effect();
            break;
        case ST7735SV_IOC_GET_STATUS:
            // Return suspend status instead of power state
            status = st7735sv_suspend_status ? 0 : 1; // 0: suspended, 1: active
            if (copy_to_user(argp, &status, sizeof(status)))
                return -EFAULT;
            break;
        case ST7735SV_IOC_SET_BRIGHTNESS:
            if (copy_from_user(&brightness, argp, sizeof(brightness)))
                return -EFAULT;
            
            // user value: 7-0, need to convert to hardware value: 0-7
            // Validate brightness range
            if (brightness < 0)
                brightness = 0;
            else if (brightness > 7)
                brightness = 7;
                
            // convert user value to hardware value: 7->0, 6->1, ..., 0->7
            brightness = 7 - brightness;
                
            // Set backlight using hardware 0-7 range
            st7735sv_set_backlight(brightness);
            break;
        case ST7735SV_IOC_GET_BRIGHTNESS:
            // convert hardware value to user value: 0->7, 1->6, ..., 7->0
            brightness = 7 - st7735sv_brightness;
            if (copy_to_user(argp, &brightness, sizeof(brightness)))
                return -EFAULT;
            break;
        default:
            return -ENOTTY;
    }
    return 0;
}

static int st7735sv_open(struct inode *inode, struct file *file) {
    return 0;
}
static int st7735sv_release(struct inode *inode, struct file *file) {
    return 0;
}

static const struct file_operations st7735sv_fops = {
    .owner = THIS_MODULE,
    .open = st7735sv_open,
    .release = st7735sv_release,
    .write = st7735sv_write,
    .unlocked_ioctl = st7735sv_ioctl,
};

static int st7735sv_init_file_node(void) {
    int ret = 0;
    ret = alloc_chrdev_region(&st7735sv_dev_no, 0, 1, "spi_lcd");
    if (ret) {
        LOG_ERR(" get major number error!\n");
        return ret;
    }
    st7735sv_cdev = cdev_alloc();
    if (!st7735sv_cdev) {
        unregister_chrdev_region(st7735sv_dev_no, 1);
        LOG_ERR(" Allocate mem for kobject failed!\n");
        return -ENOMEM;
    }
    cdev_init(st7735sv_cdev, &st7735sv_fops);
    st7735sv_cdev->owner = THIS_MODULE;
    if (cdev_add(st7735sv_cdev, st7735sv_dev_no, 1)) {
        LOG_ERR(" Attach file operation failed!\n");
        unregister_chrdev_region(st7735sv_dev_no, 1);
        return -EAGAIN;
    }
    st7735sv_class = class_create(THIS_MODULE, "spi_lcd_cls");
    if (IS_ERR(st7735sv_class)) {
        ret = PTR_ERR(st7735sv_class);
        LOG_ERR(" unable to create class, err = %d\n", ret);
        return ret;
    }
    st7735sv_device = device_create(st7735sv_class, NULL, st7735sv_dev_no, NULL, "spi_lcd"); // /dev/spi_lcd
    return ret;
}


static ssize_t st7735sv_proc_ctrl_write(struct file *filp, const char *buff, size_t size, loff_t *pos) {
    char input[8] = {0};
    long cmd = 0;
    if (size == 0 || size > 7) {
        LOG_ERR(" proc write: invalid size\n");
        return -EINVAL;
    }
    if (copy_from_user(input, buff, size) != 0) {
        LOG_ERR(" proc write: copy_from_user failed\n");
        return -EFAULT;
    }
    input[size] = '\0';
    if (kstrtol(input, 10, &cmd) == 0) {
        pr_info("  cmd = %ld\n", cmd);
        switch (cmd) {
            case 1:
                st7735sv_lcd_suspend();
                break;
            case 2:
                st7735sv_lcd_resume();
                break;
            case 3:
                show_picture_white();
                break;
            case 33:
                show_rgb_fade_effect();
                break;
            case 4:
                st7735sv_hw_reset();
                break;
            case 5:
                st7735sv_lcd_init();
                break;
            default:
                LOG_ERR(" proc write: invalid cmd %ld\n", cmd);
                return -EINVAL;
        }
        return size;
    }
    LOG_ERR(" proc write: kstrtol failed\n");
    return -EINVAL;
}
static int st7735sv_proc_ctrl_read(struct seq_file *m, void *v) {
    seq_printf(m, "ST7735SV proc control\n");
    return 0;
}
static int st7735sv_proc_ctrl_open(struct inode *inode, struct file *file) {
    return single_open(file, st7735sv_proc_ctrl_read, NULL);
} 

static ssize_t st7735sv_proc_brightness_write(struct file *filp, const char *buff, size_t size, loff_t *pos) {
    char input[8] = {0};
    long level = 0;
    
    if (size == 0 || size > 7) {
        LOG_ERR(" proc brightness write: invalid size\n");
        return -EINVAL;
    }
    
    if (copy_from_user(input, buff, size) != 0) {
        LOG_ERR(" proc brightness write: copy_from_user failed\n");
        return -EFAULT;
    }
    
    input[size] = '\0';
    if (kstrtol(input, 10, &level) != 0) {
        LOG_ERR(" proc brightness write: kstrtol failed\n");
        return -EINVAL;
    }
    
    // 0-7
    if (level < 0)
        level = 0;
    else if (level > 7)
        level = 7;
    
    // convert user value to hardware value: 7->0, 6->1, ..., 0->7
    level = 7 - level;
    
    pr_info(" Setting brightness level to %ld (hardware value)\n", level);
    st7735sv_set_backlight((u8)level);
    
    return size;
}

static int st7735sv_proc_brightness_read(struct seq_file *m, void *v) {
    // convert hardware value to user value
    int user_brightness = 7 - st7735sv_brightness;
    seq_printf(m, "Current brightness level: %d (0-7, user value)\n", user_brightness);
    return 0;
}

static int st7735sv_proc_brightness_open(struct inode *inode, struct file *file) {
    return single_open(file, st7735sv_proc_brightness_read, NULL);
}

//Framebuffer
static struct fb_info *fb_info;

/**
 * Framebuffer 空白控制 (开/关显示)
 */
static int st7735sv_fb_blank(int blank_mode, struct fb_info *info)
{
    switch (blank_mode) {
    case FB_BLANK_UNBLANK: // 开启显示
        st7735sv_lcd_resume();
        break;
    case FB_BLANK_POWERDOWN: // 关闭显示
        st7735sv_lcd_suspend();
        break;
    default:
        return -EINVAL;
    }
    return 0;
}


/**
 * 全屏刷新函数
 */
void st7735sv_fb_update(void)
{
    if (!fb_info || !fb_info->screen_buffer)
        return;
    
    // 设置显示窗口
    st7735sv_set_address_window(0, 0, ST7735SV_WIDTH-1, ST7735SV_HEIGHT-1);
    
    // 发送整个显存数据
    ST7735SV_LOCK();
    spi_lcd_send_data_buf(fb_info->screen_buffer, 
                         ST7735SV_WIDTH * ST7735SV_HEIGHT * 2);
    ST7735SV_UNLOCK();
}

static int st7735sv_fb_check_var(struct fb_var_screeninfo *var, struct fb_info *fbi)
{
    if (var->xres != ST7735SV_WIDTH || var->yres != ST7735SV_HEIGHT)
        return -EINVAL;
    
    if (var->bits_per_pixel != 16)
        return -EINVAL;
        
    return 0;
}

static int st7735sv_fb_set_par(struct fb_info *fbi)
{
    // 屏幕参数变更时重新初始化
    st7735sv_lcd_init();
    return 0;
}

static int st7735sv_fb_pan_display(struct fb_var_screeninfo *var, struct fb_info *fbi)
{
    // 实现屏幕刷新
    st7735sv_fb_update();
    return 0;
}

static int st7735sv_fb_mmap(struct fb_info *fbi, struct vm_area_struct *vma)
{
    unsigned long size = vma->vm_end - vma->vm_start;
    unsigned long offset = vma->vm_pgoff << PAGE_SHIFT;
    
    // 确保映射范围有效
    if (offset + size > fbi->fix.smem_len)
        return -EINVAL;

    // 设置 write-combining 属性（提高帧缓冲性能）
    vma->vm_page_prot = pgprot_writecombine(vma->vm_page_prot);

    // 修复：使用正确的 vmalloc 映射函数
    return remap_vmalloc_range(vma, 
                              fbi->screen_buffer + offset, 
                              vma->vm_pgoff);
}

static struct fb_ops st7735sv_fb_ops = {
    .owner = THIS_MODULE,
    .fb_check_var = st7735sv_fb_check_var,
    .fb_set_par = st7735sv_fb_set_par,
    .fb_pan_display = st7735sv_fb_pan_display,
    .fb_fillrect = cfb_fillrect,
    .fb_copyarea = cfb_copyarea,
    .fb_imageblit = cfb_imageblit,
    .fb_blank = st7735sv_fb_blank,
    .fb_mmap = st7735sv_fb_mmap,
};

/**
 * 初始化 Framebuffer 设备
 */
static int st7735sv_init_fb(struct spi_device *spi)
{
    int ret = 0;
    struct fb_info *fbi;
    
    // 正确分配 framebuffer 结构
    fbi = framebuffer_alloc(sizeof(struct stl_data), &spi->dev);
    if (!fbi) {
        dev_err(&spi->dev, "Failed to allocate framebuffer\n");
        return -ENOMEM;
    }

    fbi->fbops = &st7735sv_fb_ops;
    fbi->flags = FBINFO_FLAG_DEFAULT;
    fbi->pseudo_palette = &st7735sv_gp_data->pseudo_palette;
    
    // 设置屏幕参数
    struct fb_var_screeninfo *var = &fbi->var;
    var->xres = ST7735SV_WIDTH;
    var->yres = ST7735SV_HEIGHT;
    var->xres_virtual = var->xres;
    var->yres_virtual = var->yres;
    var->bits_per_pixel = 16;
    
    // 设置颜色格式 (RGB565)
    var->red.offset = 11; var->red.length = 5;
    var->green.offset = 5; var->green.length = 6;
    var->blue.offset = 0; var->blue.length = 5;
    var->transp.offset = 0; var->transp.length = 0;
    
    fb_alloc_cmap(&fbi->cmap, 256, 0);  // 必须的颜色映射
    
    // 设置固定参数
    struct fb_fix_screeninfo *fix = &fbi->fix;
    strlcpy(fix->id, "st7735sv", sizeof(fix->id));
    fix->type = FB_TYPE_PACKED_PIXELS;
    fix->visual = FB_VISUAL_TRUECOLOR;
    fix->line_length = ST7735SV_WIDTH * 2;
    fix->smem_len = ST7735SV_WIDTH * ST7735SV_HEIGHT * 2;
    
    // 分配显存
    fbi->screen_buffer = vmalloc(fix->smem_len);
    if (!fbi->screen_buffer) {
        ret = -ENOMEM;
        goto release_fb;
    }
    memset(fbi->screen_buffer, 0, fix->smem_len);
    
    // 注册 framebuffer
    ret = register_framebuffer(fbi);
    if (ret < 0) {
        dev_err(&spi->dev, "Failed to register framebuffer: %d\n", ret);
        goto free_mem;
    }
    
    // +++ 创建设备节点 +++
    // 1. 分配设备号
    ret = alloc_chrdev_region(&fb_dev, 0, 1, "fb");
    if (ret) {
        dev_err(&spi->dev, "Failed to allocate char device region\n");
        goto unregister_fb;
    }
    
    // 2. 创建设备类 (如果尚未创建)
    if (!st7735sv_fb_class) {
        st7735sv_fb_class = class_create(THIS_MODULE, "graphics");
        if (IS_ERR(st7735sv_fb_class)) {
            ret = PTR_ERR(st7735sv_fb_class);
            dev_err(&spi->dev, "Failed to create graphics class\n");
            goto unregister_chrdev;
        }
    }
    
    // 3. 创建设备节点
    device_create(st7735sv_fb_class, NULL, MKDEV(MAJOR(fb_dev), fb_info->node), 
                NULL, "fb%d", fb_info->node);
	device_create(st7735sv_fb_class, NULL, MKDEV(MAJOR(fb_dev), fb_info->node),  
				NULL, "graphics/fb%d", fb_info->node);
    
    // 保存到全局变量
    fb_info = fbi;
    fb_info->par = st7735sv_gp_data;
    
    dev_info(&spi->dev, "Registered framebuffer (fb%d) and created /dev/fb%d\n", 
            1, 1);
    return 0;

// 错误处理
unregister_fb:
    unregister_framebuffer(fbi);
free_mem:
    vfree(fbi->screen_buffer);
release_fb:
    framebuffer_release(fbi);
unregister_chrdev:
    unregister_chrdev_region(fb_dev, 1);
    return ret;
}
//

// 在全局变量声明区域添加
static struct fb_info *virtual_fb_info;

// 添加虚拟fb设备初始化函数
static int st7735sv_init_virtual_fb(struct spi_device *spi)
{
    int ret = 0;
    struct fb_info *fbi;
    
    // 分配虚拟framebuffer结构
    fbi = framebuffer_alloc(0, &spi->dev);
    if (!fbi) {
        dev_err(&spi->dev, "Failed to allocate virtual framebuffer\n");
        return -ENOMEM;
    }

    // 设置framebuffer操作函数
    fbi->fbops = &st7735sv_fb_ops;
    fbi->flags = FBINFO_FLAG_DEFAULT;
    
    // 设置屏幕参数
    struct fb_var_screeninfo *var = &fbi->var;
    var->xres = ST7735SV_WIDTH;
    var->yres = ST7735SV_HEIGHT;
    var->xres_virtual = var->xres;
    var->yres_virtual = var->yres;
    var->bits_per_pixel = 16;
    
    // 设置颜色格式 (RGB565)
    var->red.offset = 11; var->red.length = 5;
    var->green.offset = 5; var->green.length = 6;
    var->blue.offset = 0; var->blue.length = 5;
    var->transp.offset = 0; var->transp.length = 0;
    
    // 设置固定参数
    struct fb_fix_screeninfo *fix = &fbi->fix;
    strlcpy(fix->id, "st7735sv-virtual", sizeof(fix->id));
    fix->type = FB_TYPE_PACKED_PIXELS;
    fix->visual = FB_VISUAL_TRUECOLOR;
    fix->line_length = ST7735SV_WIDTH * 2;
    fix->smem_len = ST7735SV_WIDTH * ST7735SV_HEIGHT * 2;
    
    // 注册虚拟framebuffer
    ret = register_framebuffer(fbi);
    if (ret < 0) {
        dev_err(&spi->dev, "Failed to register virtual framebuffer: %d\n", ret);
        framebuffer_release(fbi);
        return ret;
    }
    
    virtual_fb_info = fbi;
    dev_info(&spi->dev, "Registered virtual framebuffer (fb%d)\n", fbi->node);
    
    return 0;
}

// Probe function
static int st7735sv_probe(struct spi_device *spi) {
    struct device_node *np = spi->dev.of_node;
	int ret = 0;
    
    if (!np) {
        dev_err(&spi->dev, "No device tree node found!\n");
        return -ENODEV;
    }
    
    dev_info(&spi->dev, "Probing device:\n");
    dev_info(&spi->dev, " - Compatible: %pOF\n", np);
    dev_info(&spi->dev, " - Reg: 0x%x\n", spi->chip_select);
    dev_info(&spi->dev, " - Max speed: %d Hz\n", spi->max_speed_hz);  
    
    pr_info(" probe entry, of_node_full_name: %s\n", of_node_full_name(spi->dev.of_node));
    st7735sv_gp_data->spi = spi;
    st7735sv_gp_data->speed_hz = spi->max_speed_hz;
	if (!st7735sv_gp_data) {
        LOG_ERR("gp_data is NULL!\n");
        return -ENOMEM;
    }
    if (!st7735sv_gp_data->tx_buffer) {
        st7735sv_gp_data->tx_buffer = vzalloc(ST7735SV_BUFSIZ);
        if (!st7735sv_gp_data->tx_buffer) {
            LOG_ERR("vzalloc for tx_buffer failed, size=%u\n", ST7735SV_BUFSIZ);
            ret = -ENOMEM;
            goto err_alloc_rx_buf;
        }
        pr_info("Allocated tx_buffer at %p, size=%u\n", 
               st7735sv_gp_data->tx_buffer, ST7735SV_BUFSIZ);
    }
#ifdef USING_RX_BUFFER
    if (!st7735sv_gp_data->rx_buffer) {
        st7735sv_gp_data->rx_buffer = vzalloc(st7735sv_bufsiz);
        if (!st7735sv_gp_data->rx_buffer) {
            LOG_ERR(" vzalloc rx_buffer error\n");
            ret = -ENOMEM;
            goto err_alloc_rx_buf;
        }
    }
#endif
    spi->mode = SPI_MODE_3;
    spi->bits_per_word = 8;
    ret = st7735sv_parse_dt(st7735sv_gp_data);
    if (ret) {
        LOG_ERR(" parse_dt failed\n");
        goto err_parse_dt;
    }
    ret = st7735sv_init_file_node();
    if (ret) {
        LOG_ERR(" init_file_node failed\n");
        goto err_file_node;
    }
    st7735sv_proc_dir = proc_mkdir(ST7735SV_PROC_NODE, NULL);
    if (!st7735sv_proc_dir) {
        LOG_ERR(" proc_mkdir fail\n");
        ret = -ENOMEM;
        goto err_proc_dir;
    }
    proc_create(ST7735SV_PROC_CTRL, 0, st7735sv_proc_dir, &st7735sv_proc_ctrl_fops);
    proc_create(ST7735SV_PROC_BRIGHTNESS, 0, st7735sv_proc_dir, &st7735sv_proc_brightness_fops);
    st7735sv_lcd_init();
	
	// 注册 framebuffer
    ret = st7735sv_init_fb(spi);
    if (ret) {
        dev_err(&spi->dev, "Failed to initialize framebuffer\n");
        goto err_fb_init;
    }

    // 注册虚拟 framebuffer
    ret = st7735sv_init_virtual_fb(spi);
    if (ret) {
        dev_err(&spi->dev, "Failed to initialize virtual framebuffer\n");
        // 不需要goto错误处理，因为物理fb已经初始化成功
    }
	//
	
    pr_info(" probe successful\n");
    return 0;
err_proc_dir:
    device_destroy(st7735sv_class, st7735sv_dev_no);
    class_destroy(st7735sv_class);
    cdev_del(st7735sv_cdev);
    unregister_chrdev_region(st7735sv_dev_no, 1);
err_file_node:
err_parse_dt:
err_alloc_rx_buf:
err_fb_init:
#ifdef USING_RX_BUFFER
    vfree(st7735sv_gp_data->rx_buffer);
    st7735sv_gp_data->rx_buffer = NULL;
#endif
    vfree(st7735sv_gp_data->tx_buffer);
    st7735sv_gp_data->tx_buffer = NULL;
err_find_dev:
    return ret;
}

// Remove function
static int st7735sv_remove(struct spi_device *spi) {
    pr_info(" remove entry\n");
    
    // 删除设备节点
    if (fb_info) {
        device_destroy(st7735sv_fb_class, MKDEV(MAJOR(fb_dev), fb_info->node));
    }
    
    // 注销framebuffer
    if (fb_info) {
        unregister_framebuffer(fb_info);
        vfree(fb_info->screen_buffer);
        framebuffer_release(fb_info);
        fb_info = NULL;
    }

    // 注销虚拟framebuffer
    if (virtual_fb_info) {
        unregister_framebuffer(virtual_fb_info);
        framebuffer_release(virtual_fb_info);
        virtual_fb_info = NULL;
    }
    
    // 释放设备号
    if (fb_dev) {
        unregister_chrdev_region(fb_dev, 1);
        fb_dev = 0;
    }
	
	// 注销framebuffer
    unregister_framebuffer(fb_info);
    vfree(fb_info->screen_base);
    framebuffer_release(fb_info);
	//
	
    remove_proc_entry(ST7735SV_PROC_CTRL, st7735sv_proc_dir);
    remove_proc_entry(ST7735SV_PROC_BRIGHTNESS, st7735sv_proc_dir);
    proc_remove(st7735sv_proc_dir);
    device_destroy(st7735sv_class, st7735sv_dev_no);
    class_destroy(st7735sv_class);
    cdev_del(st7735sv_cdev);
    unregister_chrdev_region(st7735sv_dev_no, 1);
    // 释放tx/rx buffer
    vfree(st7735sv_gp_data->tx_buffer);
    st7735sv_gp_data->tx_buffer = NULL;
#ifdef USING_RX_BUFFER
    vfree(st7735sv_gp_data->rx_buffer);
    st7735sv_gp_data->rx_buffer = NULL;
#endif
    // 释放GPIO
    gpio_free(st7735sv_gp_data->reset_gpio);
    gpio_free(st7735sv_gp_data->dc_ctrl_gpio);
    gpio_free(st7735sv_gp_data->en_1v8_gpio);
    gpio_free(st7735sv_gp_data->en_2v8_gpio);
    gpio_free(st7735sv_gp_data->en_led_gpio);
    return 0;
}

// Device tree match table
static const struct of_device_id st7735sv_of_match[] = {
    { .compatible = "agenew,spi_tiny_lcd" },   // 无空格版本
    { .compatible = "agenew, spi_tiny_lcd" },  // 有空格版本
    {},
};
MODULE_DEVICE_TABLE(of, st7735sv_of_match);

// SPI driver struct
static struct spi_driver st7735sv_driver = {
    .driver = {
        .name = "st7735sv_lcd",
		.owner = THIS_MODULE,
        .bus = &spi_bus_type,
        .of_match_table = st7735sv_of_match,
    },
    .probe = st7735sv_probe,
    .remove = st7735sv_remove,
};

// Module init
static int __init st7735sv_driver_init(void) {
    int err;
    pr_info("st7735sv: driver init entry\n");
    mutex_init(&st7735sv_mutex_spi_write);
    
    // 先注册SPI驱动
    err = spi_register_driver(&st7735sv_driver);
    if (err) {
        pr_err("spi_register_driver failed: %d\n", err);
        return err;
    }
    return 0;
}
module_init(st7735sv_driver_init);

static void __exit st7735sv_driver_exit(void) {
    spi_unregister_driver(&st7735sv_driver);
    pr_info("st7735sv: driver unloaded\n");
}
module_exit(st7735sv_driver_exit);

// 确保在SPI控制器就绪后加载驱动
static int spi_notifier_call(struct notifier_block *nb,
                            unsigned long event, void *data) {
    struct device *dev = data;
    
    if (event == BUS_NOTIFY_BOUND_DRIVER) {
        if (strstr(dev_name(dev), "spi5")) {
            pr_info("SPI5 controller ready, loading display driver\n");
            st7735sv_driver_init();
        }
    }
    return NOTIFY_DONE;
}

static struct notifier_block spi_nb = {
    .notifier_call = spi_notifier_call,
};

// 在模块初始化中注册通知
static int __init st7735sv_watchdog_init(void) {
    bus_register_notifier(&spi_bus_type, &spi_nb);
    return 0;
}
subsys_initcall(st7735sv_watchdog_init);

MODULE_DESCRIPTION("ST7735SV SPI LCD Driver");
MODULE_LICENSE("GPL");