/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifdef MTK_SF_AIDL
#include "MtkSF_ext.h"

#include <android-base/logging.h>
#include <log/log.h>

namespace aidl {
namespace vendor {
namespace mediatek {
namespace framework {
namespace mtksf_ext {

using ::ndk::ScopedAStatus;

ndk::ScopedAStatus MtkSF_ext::setDisplayDejitterConfig(int64_t fps, int64_t pbc, bool* result) {
    ALOGV("MtkSF_ext::setDisplayDejitterConfig %" PRId64 ", %" PRId64, fps, pbc);
#ifdef MTK_DISPLAY_DEJITTER
    *result = mFlinger->setDisplayDejitterConfig(fps, pbc);
#endif
    return ndk::ScopedAStatus::ok();
}

void MtkSF_ext::setFlinger(::android::SurfaceFlinger* flinger) {
    LOG(INFO) << "Get flinger";
    mFlinger = flinger;
}

}  // namespace mtksf_ext
}  // namespace framework
}  // namespace mediatek
}  // namespace vendor
}  // namespace aidl
#endif