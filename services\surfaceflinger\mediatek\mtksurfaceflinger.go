package surfaceflinger

import (
	"android/soong/android"
	"android/soong/cc"
)

func mtkSurfaceflingerDefaults(ctx android.LoadHookContext) {
	type props struct {
		Cflags []string
		Shared_libs []string
	}
	p := &props{}
	vars := ctx.Config().VendorConfig("mtkPlugin")

	if (vars.String("MTK_CUTTLESTONE") == "yes") {
		p.Cflags = append(p.Cflags, "-DMTK_CUTTLESTONE")
	}
	ctx.AppendProperties(p)
}

func init() {
	android.RegisterModuleType("mtk_surfaceflinger_config_defaults", mtkSurfaceflingerDefaultsFactory)
}

func mtkSurfaceflingerDefaultsFactory() android.Module {
	module := cc.DefaultsFactory()
	android.AddLoadHook(module, mtkSurfaceflingerDefaults)
	return module
}
