/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef HIDUTIL_TEST_HIDDESCRIPTOR_H_
#define HIDUTIL_TEST_HIDDESCRIPTOR_H_

#include <cstddef>

struct TestHidDescriptor {
    const unsigned char *data;
    size_t len;
    const char *name;
};

extern const TestHidDescriptor gDescriptorArray[];
const TestHidDescriptor *findTestDescriptor(const char *name);

#endif // HIDUTIL_TEST_HIDDESCRIPTOR_H_
