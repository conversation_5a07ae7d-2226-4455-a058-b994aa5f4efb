// This file is autogenerated by hidl-gen. Do not edit manually.
// Source: android.hardware.gnss@1.0
// Location: hardware/interfaces/gnss/1.0/

#ifndef HIDL_GENERATED_ANDROID_HARDWARE_GNSS_V1_0_EXPORTED_CONSTANTS_H_
#define HIDL_GENERATED_ANDROID_HARDWARE_GNSS_V1_0_EXPORTED_CONSTANTS_H_

#ifdef __cplusplus
extern "C" {
#endif

enum {
    GNSS_MAX_SVS_COUNT = 64u,
};

enum {
    GNSS_CONSTELLATION_UNKNOWN = 0,
    GNSS_CONSTELLATION_GPS = 1,
    GNSS_CONSTELLATION_SBAS = 2,
    GNSS_CONSTELLATION_GLONASS = 3,
    GNSS_CONSTELLATION_QZSS = 4,
    GNSS_CONSTELLATION_BEIDOU = 5,
    GNSS_CONSTELLATION_GALILEO = 6,
};

enum {
    GPS_LOCATION_HAS_LAT_LONG = 1 /* 0x0001 */,
    GPS_LOCATION_HAS_ALTITUDE = 2 /* 0x0002 */,
    GPS_LOCATION_HAS_SPEED = 4 /* 0x0004 */,
    GPS_LOCATION_HAS_BEARING = 8 /* 0x0008 */,
    GPS_LOCATION_HAS_HORIZONTAL_ACCURACY = 16 /* 0x0010 */,
    GPS_LOCATION_HAS_VERTICAL_ACCURACY = 32 /* 0x0020 */,
    GPS_LOCATION_HAS_SPEED_ACCURACY = 64 /* 0x0040 */,
    GPS_LOCATION_HAS_BEARING_ACCURACY = 128 /* 0x0080 */,
};

enum {
    APN_IP_INVALID = 0,
    APN_IP_IPV4 = 1,
    APN_IP_IPV6 = 2,
    APN_IP_IPV4V6 = 3,
};

enum {
    AGPS_TYPE_SUPL = 1,
    AGPS_TYPE_C2K = 2,
};

enum {
    GNSS_REQUEST_AGNSS_DATA_CONN = 1,
    GNSS_RELEASE_AGNSS_DATA_CONN = 2,
    GNSS_AGNSS_DATA_CONNECTED = 3,
    GNSS_AGNSS_DATA_CONN_DONE = 4,
    GNSS_AGNSS_DATA_CONN_FAILED = 5,
};

enum {
    AGPS_SETID_TYPE_NONE = 0,
    AGPS_SETID_TYPE_IMSI = 1,
    AGPS_SETID_TYPE_MSISDM = 2,
};

enum {
    AGPS_RIL_NETWORK_TYPE_MOBILE = 0,
    AGPS_RIL_NETWORK_TYPE_WIFI = 1,
    AGPS_RIL_NETWORK_TYPE_MMS = 2,
    AGPS_RIL_NETWORK_TYPE_SUPL = 3,
    AGPS_RIL_NETWORK_TYPE_DUN = 4,
    AGPS_RIL_NETWORK_TYPE_HIPRI = 5,
    AGPS_RIL_NETWORK_TYPE_WIMAX = 6,
};

enum {
    AGPS_REF_LOCATION_TYPE_GSM_CELLID = 1,
    AGPS_REF_LOCATION_TYPE_UMTS_CELLID = 2,
    AGPS_REF_LOCATION_TYPE_LTE_CELLID = 4,
};

enum {
    AGPS_RIL_REQUEST_SETID_IMSI = 1u /* (1 << 0L) */,
    AGPS_RIL_REQUEST_SETID_MSISDN = 2u /* (1 << 1L) */,
};

enum {
    GPS_POSITION_MODE_STANDALONE = 0,
    GPS_POSITION_MODE_MS_BASED = 1,
    GPS_POSITION_MODE_MS_ASSISTED = 2,
};

enum {
    GPS_POSITION_RECURRENCE_PERIODIC = 0u,
    GPS_POSITION_RECURRENCE_SINGLE = 1u,
};

enum {
    GPS_DELETE_EPHEMERIS = 1 /* 0x0001 */,
    GPS_DELETE_ALMANAC = 2 /* 0x0002 */,
    GPS_DELETE_POSITION = 4 /* 0x0004 */,
    GPS_DELETE_TIME = 8 /* 0x0008 */,
    GPS_DELETE_IONO = 16 /* 0x0010 */,
    GPS_DELETE_UTC = 32 /* 0x0020 */,
    GPS_DELETE_HEALTH = 64 /* 0x0040 */,
    GPS_DELETE_SVDIR = 128 /* 0x0080 */,
    GPS_DELETE_SVSTEER = 256 /* 0x0100 */,
    GPS_DELETE_SADATA = 512 /* 0x0200 */,
    GPS_DELETE_RTI = 1024 /* 0x0400 */,
    GPS_DELETE_CELLDB_INFO = 32768 /* 0x8000 */,
    GPS_DELETE_ALL = 65535 /* 0xFFFF */,
};

enum {
    FLP_BATCH_WAKEUP_ON_FIFO_FULL = 1 /* 0x01 */,
};

enum {
    GPS_CAPABILITY_SCHEDULING = 1u /* (1 << 0) */,
    GPS_CAPABILITY_MSB = 2u /* (1 << 1) */,
    GPS_CAPABILITY_MSA = 4u /* (1 << 2) */,
    GPS_CAPABILITY_SINGLE_SHOT = 8u /* (1 << 3) */,
    GPS_CAPABILITY_ON_DEMAND_TIME = 16u /* (1 << 4) */,
    GPS_CAPABILITY_GEOFENCING = 32u /* (1 << 5) */,
    GPS_CAPABILITY_MEASUREMENTS = 64u /* (1 << 6) */,
    GPS_CAPABILITY_NAV_MESSAGES = 128u /* (1 << 7) */,
};

enum {
    GPS_STATUS_NONE = 0,
    GPS_STATUS_SESSION_BEGIN = 1,
    GPS_STATUS_SESSION_END = 2,
    GPS_STATUS_ENGINE_ON = 3,
    GPS_STATUS_ENGINE_OFF = 4,
};

enum {
    GNSS_SV_FLAGS_NONE = 0,
    GNSS_SV_FLAGS_HAS_EPHEMERIS_DATA = 1 /* (1 << 0) */,
    GNSS_SV_FLAGS_HAS_ALMANAC_DATA = 2 /* (1 << 1) */,
    GNSS_SV_FLAGS_USED_IN_FIX = 4 /* (1 << 2) */,
    GNSS_SV_FLAGS_HAS_CARRIER_FREQUENCY = 8 /* (1 << 3) */,
};

enum {
    GPS_GEOFENCE_ENTERED = 1 /* (1 << 0L) */,
    GPS_GEOFENCE_EXITED = 2 /* (1 << 1L) */,
    GPS_GEOFENCE_UNCERTAIN = 4 /* (1 << 2L) */,
};

enum {
    GPS_GEOFENCE_UNAVAILABLE = 1 /* (1 << 0L) */,
    GPS_GEOFENCE_AVAILABLE = 2 /* (1 << 1L) */,
};

enum {
    GPS_GEOFENCE_OPERATION_SUCCESS = 0,
    GPS_GEOFENCE_ERROR_TOO_MANY_GEOFENCES = -100 /* (-100) */,
    GPS_GEOFENCE_ERROR_ID_EXISTS = -101 /* (-101) */,
    GPS_GEOFENCE_ERROR_ID_UNKNOWN = -102 /* (-102) */,
    GPS_GEOFENCE_ERROR_INVALID_TRANSITION = -103 /* (-103) */,
    GPS_GEOFENCE_ERROR_GENERIC = -149 /* (-149) */,
};

enum {
    GPS_MEASUREMENT_SUCCESS = 0,
    GPS_MEASUREMENT_ERROR_ALREADY_INIT = -100 /* (-100) */,
    GPS_MEASUREMENT_ERROR_GENERIC = -101 /* (-101) */,
};

enum {
    GNSS_CLOCK_HAS_LEAP_SECOND = 1 /* (1 << 0) */,
    GNSS_CLOCK_HAS_TIME_UNCERTAINTY = 2 /* (1 << 1) */,
    GNSS_CLOCK_HAS_FULL_BIAS = 4 /* (1 << 2) */,
    GNSS_CLOCK_HAS_BIAS = 8 /* (1 << 3) */,
    GNSS_CLOCK_HAS_BIAS_UNCERTAINTY = 16 /* (1 << 4) */,
    GNSS_CLOCK_HAS_DRIFT = 32 /* (1 << 5) */,
    GNSS_CLOCK_HAS_DRIFT_UNCERTAINTY = 64 /* (1 << 6) */,
};

enum {
    GNSS_MEASUREMENT_HAS_SNR = 1u /* (1 << 0) */,
    GNSS_MEASUREMENT_HAS_CARRIER_FREQUENCY = 512u /* (1 << 9) */,
    GNSS_MEASUREMENT_HAS_CARRIER_CYCLES = 1024u /* (1 << 10) */,
    GNSS_MEASUREMENT_HAS_CARRIER_PHASE = 2048u /* (1 << 11) */,
    GNSS_MEASUREMENT_HAS_CARRIER_PHASE_UNCERTAINTY = 4096u /* (1 << 12) */,
    GNSS_MEASUREMENT_HAS_AUTOMATIC_GAIN_CONTROL = 8192u /* (1 << 13) */,
};

enum {
    GNSS_MULTIPATH_INDICATOR_UNKNOWN = 0,
    GNSS_MULTIPATH_INDICATOR_PRESENT = 1,
    GNSS_MULTIPATH_INDICATIOR_NOT_PRESENT = 2,
};

enum {
    GNSS_MEASUREMENT_STATE_UNKNOWN = 0u,
    GNSS_MEASUREMENT_STATE_CODE_LOCK = 1u /* (1 << 0) */,
    GNSS_MEASUREMENT_STATE_BIT_SYNC = 2u /* (1 << 1) */,
    GNSS_MEASUREMENT_STATE_SUBFRAME_SYNC = 4u /* (1 << 2) */,
    GNSS_MEASUREMENT_STATE_TOW_DECODED = 8u /* (1 << 3) */,
    GNSS_MEASUREMENT_STATE_MSEC_AMBIGUOUS = 16u /* (1 << 4) */,
    GNSS_MEASUREMENT_STATE_SYMBOL_SYNC = 32u /* (1 << 5) */,
    GNSS_MEASUREMENT_STATE_GLO_STRING_SYNC = 64u /* (1 << 6) */,
    GNSS_MEASUREMENT_STATE_GLO_TOD_DECODED = 128u /* (1 << 7) */,
    GNSS_MEASUREMENT_STATE_BDS_D2_BIT_SYNC = 256u /* (1 << 8) */,
    GNSS_MEASUREMENT_STATE_BDS_D2_SUBFRAME_SYNC = 512u /* (1 << 9) */,
    GNSS_MEASUREMENT_STATE_GAL_E1BC_CODE_LOCK = 1024u /* (1 << 10) */,
    GNSS_MEASUREMENT_STATE_GAL_E1C_2ND_CODE_LOCK = 2048u /* (1 << 11) */,
    GNSS_MEASUREMENT_STATE_GAL_E1B_PAGE_SYNC = 4096u /* (1 << 12) */,
    GNSS_MEASUREMENT_STATE_SBAS_SYNC = 8192u /* (1 << 13) */,
    GNSS_MEASUREMENT_STATE_TOW_KNOWN = 16384u /* (1 << 14) */,
    GNSS_MEASUREMENT_STATE_GLO_TOD_KNOWN = 32768u /* (1 << 15) */,
};

enum {
    GNSS_ADR_STATE_UNKNOWN = 0,
    GNSS_ADR_STATE_VALID = 1 /* (1 << 0) */,
    GNSS_ADR_STATE_RESET = 2 /* (1 << 1) */,
    GNSS_ADR_STATE_CYCLE_SLIP = 4 /* (1 << 2) */,
};

enum {
    GPS_NAVIGATION_MESSAGE_SUCCESS = 0,
    GPS_NAVIGATION_MESSAGE_ERROR_ALREADY_INIT = -100 /* (-100) */,
    GPS_NAVIGATION_MESSAGE_ERROR_GENERIC = -101 /* (-101) */,
};

enum {
    GNSS_NAVIGATION_MESSAGE_TYPE_UNKNOWN = 0,
    GNSS_NAVIGATION_MESSAGE_TYPE_GPS_L1CA = 257 /* 0x0101 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_GPS_L2CNAV = 258 /* 0x0102 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_GPS_L5CNAV = 259 /* 0x0103 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_GPS_CNAV2 = 260 /* 0x0104 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_GLO_L1CA = 769 /* 0x0301 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_BDS_D1 = 1281 /* 0x0501 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_BDS_D2 = 1282 /* 0x0502 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_GAL_I = 1537 /* 0x0601 */,
    GNSS_NAVIGATION_MESSAGE_TYPE_GAL_F = 1538 /* 0x0602 */,
};

typedef enum {
    NAV_MESSAGE_STATUS_PARITY_PASSED = 1 /* (1 << 0) */,
    NAV_MESSAGE_STATUS_PARITY_REBUILT = 2 /* (1 << 1) */,
    NAV_MESSAGE_STATUS_UNKNOWN = 0,
} navigation_message_status;

enum {
    GPS_NI_TYPE_VOICE = 1,
    GPS_NI_TYPE_UMTS_SUPL = 2,
    GPS_NI_TYPE_UMTS_CTRL_PLANE = 3,
    GPS_NI_TYPE_EMERGENCY_SUPL = 4,
};

enum {
    GPS_NI_NEED_NOTIFY = 1u /* 0x0001 */,
    GPS_NI_NEED_VERIFY = 2u /* 0x0002 */,
    GPS_NI_PRIVACY_OVERRIDE = 4u /* 0x0004 */,
};

enum {
    GPS_NI_RESPONSE_ACCEPT = 1,
    GPS_NI_RESPONSE_DENY = 2,
    GPS_NI_RESPONSE_NORESP = 3,
};

enum {
    GPS_ENC_NONE = 0,
    GPS_ENC_SUPL_GSM_DEFAULT = 1,
    GPS_ENC_SUPL_UTF8 = 2,
    GPS_ENC_SUPL_UCS2 = 3,
    GPS_ENC_UNKNOWN = -1 /* (-1) */,
};

#ifdef __cplusplus
}
#endif

#endif  // HIDL_GENERATED_ANDROID_HARDWARE_GNSS_V1_0_EXPORTED_CONSTANTS_H_
