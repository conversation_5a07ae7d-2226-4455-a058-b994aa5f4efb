/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ANDROID_SWITCH_INPUT_MAPPER_H_
#define ANDROID_SWITCH_INPUT_MAPPER_H_

#include <cstdint>

#include <utils/BitSet.h>
#include <utils/Timers.h>

#include "InputMapper.h"

namespace android {

class SwitchInputMapper : public InputMapper {
public:
    SwitchInputMapper();
    virtual ~SwitchInputMapper() = default;

    virtual bool configureInputReport(InputDeviceNode* devNode,
            InputReportDefinition* report) override;
    virtual void process(const InputEvent& event) override;

private:
    void processSwitch(int32_t switchCode, int32_t switchValue);
    void sync(nsecs_t when);

    BitSet64 mSwitchValues;
    BitSet64 mUpdatedSwitchMask;
};

}  // namespace android

#endif  // ANDROID_SWITCH_INPUT_MAPPER_H_
