/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2021. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */
#if ((defined MTK_SF_CPU_POLICY) || (defined MTK_SF_CPU_POLICY_FOR_LEGACY))
#ifndef SF_CPU_POLICY_ADAPTER_H
#define SF_CPU_POLICY_ADAPTER_H
#include "SurfaceFlinger.h"
#include "sf_cpu/SfCpuPolicy.h"
#include "FrameTimeline/FrameTimeline.h"

#ifndef MTK_CUTTLESTONE
#include <ui/gralloc_extra.h>
#endif //MTK_CUTTLESTONE

#define VPLP_LAYER_UNCHECKED -1
#define VPLP_LAYER_CHECKED_NOT_OK 0
#define VPLP_LAYER_CHECKED_OK 1

#ifdef MTK_COMPOSER_EXT
namespace ComposerExt {
class ClientInterface;
}
#endif

namespace android {

class SfCpuPolicyInterface {
public:
    virtual ~SfCpuPolicyInterface() {};
    virtual bool needIncreasePerf(nsecs_t startTime, nsecs_t endTime, nsecs_t targetTime) = 0;
    virtual bool needReducePerf(nsecs_t startTime, nsecs_t endTime, nsecs_t targetTime) = 0;
    virtual void setupConfig(SfCpuPolicy::Config & conf) = 0;
    virtual void notifyFrameStart(nsecs_t startTime, int64_t vsyncId) = 0;
    virtual void notifySpeedUpRE(int resource) = 0;
    virtual void notifyFrameEnd(nsecs_t endTime) = 0;
    virtual void notifyClearCorrection(bool clear, bool heavyLoading) = 0;
    virtual void notifyResetCalculation() = 0;
    virtual void notifyPowerSuspend(bool enable) = 0;
    virtual void notifyHwcHwbinderTid(int tid) = 0;
    virtual void changeToForeground() = 0;
    virtual void notifyVpLpEnable(bool enable) = 0;
    virtual void notifyLayerConnect(const void * token, const std::string& name) = 0;
    virtual void notifyLayerDisconnect(const void * token) = 0;
    virtual void notifyLayerSetBuffer(const void * token, const sp<GraphicBuffer>& buffer) = 0;
    virtual bool checkIfHeavy (bool earlySchedule, bool dp, bool userHeavy, bool gpu) = 0;
    virtual SfCpuPolicy::EnableReason checkIfNeedSuspend(int rate, bool gpu_comp, bool * sus_mid,  nsecs_t *targetTime, nsecs_t sfDuration) = 0;
    virtual nsecs_t calcTargetTime (int fps, nsecs_t sfDuration) = 0;
#ifdef MTK_COMPOSER_EXT
    virtual void setComposerExtIntf(ComposerExt::ClientInterface* intf) = 0;
#endif
};

#ifdef MTK_SF_CPU_POLICY
class SfCpuPolicyAdapter: public SfCpuPolicyInterface, public virtual SfCpuPolicy::PerfMeas{
public:
    static SfCpuPolicyAdapter& getInstance(frametimeline::FrameTimeline & frameTimeline);
    static bool isEnabled();
    static bool foregroundEnabled();
    SfCpuPolicyAdapter(frametimeline::FrameTimeline & frameTimeline);
    ~SfCpuPolicyAdapter();
    bool needIncreasePerf(nsecs_t startTime, nsecs_t endTime, nsecs_t targetTime) override; //override
    bool needReducePerf(nsecs_t startTime, nsecs_t endTime, nsecs_t targetTime) override; //override
    SfCpuPolicy* createSfCpuPolicy();
    void destroySfCpuPolicy();
    void setupConfig(SfCpuPolicy::Config & conf) override;
    void notifyFrameStart(nsecs_t startTime, int64_t vsyncId) override;
    void notifySpeedUpRE(int resource) override;
    void notifyFrameEnd(nsecs_t endTime) override;
    void notifyClearCorrection(bool clear, bool heavyLoading) override;
    void notifyResetCalculation() override;
    void notifyPowerSuspend(bool enable) override;
    void notifyHwcHwbinderTid(int tid) override;
    void changeToForeground() override;
    void notifyVpLpEnable(bool enable) override;
    void notifyLayerConnect(const void * token, const std::string& name) override;
    void notifyLayerDisconnect(const void * token) override;
    void notifyLayerSetBuffer(const void * token, const sp<GraphicBuffer>& buffer) override;
    bool checkIfHeavy (bool earlySchedule, bool dp, bool userHeavy, bool gpu) override;
    SfCpuPolicy::EnableReason checkIfNeedSuspend(int rate, bool gpu_comp, bool * sus_mid,  nsecs_t *targetTime, nsecs_t sfDuration) override;
    nsecs_t calcTargetTime (int fps, nsecs_t sfDuration) override;
    const static SfCpuPolicy::Config HighPerfCong;
    const static SfCpuPolicy::Config NormalPerfCong;
#ifdef MTK_COMPOSER_EXT
    void setComposerExtIntf(ComposerExt::ClientInterface* intf) override;
#endif
protected:
    SfCpuPolicy * mCpuPolicy = nullptr;
    frametimeline::FrameTimeline & mFrameTimeline;;
    void* mSoHandle;
    SfCpuPolicy* (*mFnCreateSfCpuPolicy)();
    void (*mFnDestroySfCpuPolicy)(SfCpuPolicy* cpuPolicy);
    void (*mFnSetupConfig)(SfCpuPolicy* cpuPolicy,SfCpuPolicy::Config & conf, bool suspend);
    void (*mFnSetupPerfMeas)(SfCpuPolicy* cpuPolicy, SfCpuPolicy::PerfMeas * perfMeas);
    void (*mFnNotifyFrameStart)(SfCpuPolicy* cpuPolicy, nsecs_t startTime, int64_t vsyncId, bool suspend);
    void (*mFnNotifySpeedUpRE)(SfCpuPolicy* cpuPolicy, int resource, int64_t vsyncId);
    void (*mFnNotifyFrameEnd)(SfCpuPolicy* cpuPolicy, nsecs_t endTime, int64_t vsyncId, bool suspend);
    void (*mFnNotifyClearCorrection)(SfCpuPolicy* cpuPolicy, int64_t vsyncId, bool clear, bool heavyLoading);
    void (*mFnNotifyResetCalculation)(SfCpuPolicy* cpuPolicy, int64_t vsyncId);
    void (*mFnNotifyHwcHwbinderTid)(SfCpuPolicy* cpuPolicy, int tid);
    void (*mFnChangeToForeground)(SfCpuPolicy* cpuPolicy);
private:
    void InitProp();
    bool getVpLpEnable();
    bool mIsLastFrameFinished = true;
    nsecs_t mLastFrameEndTime = 0;
    int64_t mLastVsyncId = 0;
    bool mPowerSuspend = false;
    bool mSuspendNeed = false;
    bool mSuspend = false;
    float mLastFps = 0.0f;
    bool mVpLpEnable = false;
    bool mVpLpEnableDbg = false;
    mutable std::mutex mMutex;
    std::map<const void *, int>  mVpLpLayers; //value -1 (not checked), 0 (checked, not VP ), 1 (checked, VP layer)
    float mS2sFps = 0;
#ifdef MTK_COMPOSER_EXT
    bool mLastVpLpState = false;
    ComposerExt::ClientInterface* mMtkComposerExtIntf = nullptr;
#endif
};
#endif //MTK_SF_CPU_POLICY

#ifdef MTK_SF_CPU_POLICY_FOR_LEGACY
class SfLegacyCpuPolicyAdapter: public SfCpuPolicyInterface {
public:
    static SfLegacyCpuPolicyAdapter& getInstance(frametimeline::FrameTimeline & frameTimeline);
    static bool isEnabled();
    SfLegacyCpuPolicyAdapter(frametimeline::FrameTimeline & frameTimeline);
    ~SfLegacyCpuPolicyAdapter();
    bool needIncreasePerf(nsecs_t startTime, nsecs_t endTime, nsecs_t targetTime) override;
    bool needReducePerf(nsecs_t startTime, nsecs_t endTime, nsecs_t targetTime) override;
    void setupConfig(SfCpuPolicy::Config & conf) override;
    void notifyFrameStart(nsecs_t startTime, int64_t vsyncId) override;
    void notifySpeedUpRE(int resource) override;
    void notifyFrameEnd(nsecs_t endTime) override;
    void notifyClearCorrection(bool clear, bool heavyLoading) override;
    void notifyResetCalculation() override;
    void notifyPowerSuspend(bool enable) override;
    void notifyHwcHwbinderTid(int tid) override;
    void changeToForeground() override;
    void notifyVpLpEnable(bool enable) override;
    void notifyLayerConnect(const void * token, const std::string& name) override;
    void notifyLayerDisconnect(const void * token) override;
    void notifyLayerSetBuffer(const void * token, const sp<GraphicBuffer>& buffer) override;
    bool checkIfHeavy (bool earlySchedule, bool dp, bool userHeavy, bool gpu) override;
    SfCpuPolicy::EnableReason checkIfNeedSuspend(int rate, bool gpu_comp, bool * sus_mid,  nsecs_t *targetTime, nsecs_t sfDuration) override;
    nsecs_t calcTargetTime (int fps, nsecs_t sfDuration) override;
    const static SfCpuPolicy::Config AnimationCong;
#ifdef MTK_COMPOSER_EXT
    void setComposerExtIntf(ComposerExt::ClientInterface* intf) override;
#endif
protected:
    frametimeline::FrameTimeline & mFrameTimeline;

private:
    struct FrameInfo {
        public:
            int64_t vsyncId;
            nsecs_t startTime;
            nsecs_t endTime;
            bool clearCorrection;
            bool heavyLoading;
    };
    enum MinFps{
        FPS_60 = 35,
        FPS_90 = 65,
        FPS_120 = 95,
    };

    FrameInfo * getFrameById(int64_t vsyncId);
    void initProp();
    void calcAndUpdateUclamp(FrameInfo * info);
    void calcCorrection(FrameInfo * info);
    void changeToSystem();
    void cpuPolicyThread();
    void frameInfoClear(FrameInfo * info);
    void resetUclamp();
    void setUclamp();

private:
    mutable std::mutex mMutex;
    mutable std::mutex mHwcMutex;
    mutable std::mutex mCpuPolicyThreadMutex;
    mutable std::condition_variable mCondition;
    mutable std::queue<std::function<void()>> mTasks
            GUARDED_BY(mCpuPolicyThreadMutex);
    std::thread mCpuPolicyThread;
    std::vector<int> mReadyTid;
    std::vector<int> mCgroup;

    bool mThreadStarted = false;
    bool mCpuSystemOnly = true;
    bool mIsLastFrameFinished = true;
    bool mPowerSuspend = false;
    bool mPolicySuspend = true;
    bool mIsEarly = false;
    bool mResetUclamp = false;
    bool mNeedBoost = false;

    FrameInfo frameList[5] = {};
    int64_t mLastVsyncId = 0;
    int mBaseUclampMin = 0;
    int mTargetUclampMin = 0;
    int mCorrectionOffset = 0;
    int mUpCount = 0;
    int mDownCount = 0;
    nsecs_t mPowerUpStartTime = 0;
    nsecs_t mPowerDownStartTime = 0;
    SfCpuPolicy::Config mConf;
};
#endif //MTK_SF_CPU_POLICY_FOR_LEGACY
} // namespace android
#endif
#endif
