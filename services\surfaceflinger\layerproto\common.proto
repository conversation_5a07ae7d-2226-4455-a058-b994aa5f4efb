/*
 * Copyright (C) 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";
option optimize_for = LITE_RUNTIME;
package android.surfaceflinger;

message RegionProto {
    reserved 1; // Previously: uint64 id
    repeated RectProto rect = 2;
}

message RectProto {
  int32 left   = 1;
  int32 top    = 2;
  int32 right  = 3;
  int32 bottom = 4;
}

message SizeProto {
  int32 w = 1;
  int32 h = 2;
}

message TransformProto {
  float dsdx = 1;
  float dtdx = 2;
  float dsdy = 3;
  float dtdy = 4;
  int32 type = 5;
}

message ColorProto {
    float r = 1;
    float g = 2;
    float b = 3;
    float a = 4;
}

message InputWindowInfoProto {
    uint32 layout_params_flags = 1;
    int32 layout_params_type = 2;
    RectProto frame = 3;
    RegionProto touchable_region = 4;

    int32 surface_inset = 5;
    bool visible = 6;
    bool can_receive_keys = 7 [deprecated = true];
    bool focusable = 8;
    bool has_wallpaper = 9;

    float global_scale_factor = 10;
    float window_x_scale = 11 [deprecated = true];
    float window_y_scale = 12 [deprecated = true];

    int32 crop_layer_id = 13;
    bool replace_touchable_region_with_crop = 14;
    RectProto touchable_region_crop = 15;
    TransformProto transform = 16;
}

message BlurRegion {
    uint32 blur_radius = 1;
    uint32 corner_radius_tl = 2;
    uint32 corner_radius_tr = 3;
    uint32 corner_radius_bl = 4;
    float corner_radius_br = 5;
    float alpha = 6;
    int32 left = 7;
    int32 top = 8;
    int32 right = 9;
    int32 bottom = 10;
}

message ColorTransformProto {
    // This will be a 4x4 matrix of float values
    repeated float val = 1;
}
