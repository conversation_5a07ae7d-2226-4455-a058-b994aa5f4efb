/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto2";
option optimize_for = LITE_RUNTIME;

import "frameworks/native/services/surfaceflinger/layerproto/layers.proto";
import "frameworks/native/services/surfaceflinger/layerproto/display.proto";

package android.surfaceflinger;

/* represents a file full of surface flinger trace entries.
   Encoded, it should start with 0x4c 0x59 0x52 0x54 0x52 0x41 0x43 0x45 (.LYRTRACE), such
   that they can be easily identified. */
message LayersTraceFileProto {

    /* constant; MAGIC_NUMBER = (long) MAGIC_NUMBER_H << 32 | MagicNumber.MAGIC_NUMBER_L
       (this is needed because enums have to be 32 bits and there's no nice way to put 64bit
        constants into .proto files. */
    enum MagicNumber {
        INVALID = 0;
        MAGIC_NUMBER_L = 0x5452594c;  /* LYRT (little-endian ASCII) */
        MAGIC_NUMBER_H = 0x45434152;  /* RACE (little-endian ASCII) */
    }

    optional fixed64 magic_number = 1;  /* Must be the first field, set to value in MagicNumber */
    repeated LayersTraceProto entry = 2;
}

/* one window manager trace entry. */
message LayersTraceProto {
    /* required: elapsed realtime in nanos since boot of when this entry was logged */
    optional sfixed64 elapsed_realtime_nanos = 1;

    /* where the trace originated */
    optional string where = 2;

    optional LayersProto layers = 3;

    // Blob for the current HWC information for all layers, reported by dumpsys.
    optional string hwc_blob = 4;

    /* Includes state sent during composition like visible region and composition type. */
    optional bool excludes_composition_state = 5;

    /* Number of missed entries since the last entry was recorded. */
    optional uint32 missed_entries = 6;

    repeated DisplayProto displays = 7;
}
