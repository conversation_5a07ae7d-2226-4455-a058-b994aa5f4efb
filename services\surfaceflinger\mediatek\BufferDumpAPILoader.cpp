#define ATRACE_TAG ATRACE_TAG_GRAPHICS
//#define LOG_NDEBUG 0
//#define MTK_LOG_ENABLE 1
#include <cmath>
#include <dlfcn.h>

#include <cutils/properties.h>
#include <log/log.h>
#include <binder/IPCThreadState.h>
#include <mediatek/BufferDumpAPILoader.h>

namespace android {
// -----------------------------------------------------------------------------

status_t getProcessName(int pid, std::string& name) {
    std::string fileName = "/proc/" + std::to_string(pid) + "/cmdline";
    FILE *fp = fopen(fileName.c_str(), "r");
    if (NULL != fp) {
        const size_t size = 64;
        char proc_name[size] = {0};
        char* result = fgets(proc_name, size - 1, fp);
        int ret = fclose(fp);
        if (CC_UNLIKELY(ret != 0)) {
            ALOGE("%s(), fclose fail", __FUNCTION__);
        }
        if (CC_LIKELY(result != nullptr)) {
            name = proc_name;
            return NO_ERROR;
        }
    }
    return INVALID_OPERATION;
}

// -----------------------------------------------------------------------------
GuiDebugModuleLoader& GuiDebugModuleLoader::getInstance() {
    static GuiDebugModuleLoader gInstance;
    return gInstance;
}

GuiDebugModuleLoader::GuiDebugModuleLoader() :
    mBQDumpSoHandle(NULL),
    mCreateBQDumpInstancePtr(NULL)
{
    typedef BufferQueueDumpAPI *(*createBQdumpPrototype)();
    mBQDumpSoHandle = dlopen("libgui_debug.so", RTLD_LAZY);
    if (mBQDumpSoHandle) {
        mCreateBQDumpInstancePtr = reinterpret_cast<createBQdumpPrototype>(
                dlsym(mBQDumpSoHandle, "createBQDumpInstance"));
        if (mCreateBQDumpInstancePtr == NULL) {
            ALOGD("Can't load func mCreateBQDumpInstancePtr");
        }
    } else {
        ALOGD("Can't load libgui_debug");
    }
}

GuiDebugModuleLoader::~GuiDebugModuleLoader() {
    if (mBQDumpSoHandle != NULL) {
        dlclose(mBQDumpSoHandle);
    }
}

BufferQueueDumpAPI* GuiDebugModuleLoader::CreateBQDumpInstance() {
    if (mCreateBQDumpInstancePtr) {
        return mCreateBQDumpInstancePtr();
    } else {
        return NULL;
    }
}
}; // namespace android
