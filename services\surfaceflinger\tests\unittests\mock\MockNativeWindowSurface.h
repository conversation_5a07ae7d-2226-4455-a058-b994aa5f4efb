/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <gmock/gmock.h>

#include <system/window.h> // for ANativeWindow

#include "NativeWindowSurface.h"

namespace android::surfaceflinger::mock {

class NativeWindowSurface : public surfaceflinger::NativeWindowSurface {
public:
    NativeWindowSurface();
    ~NativeWindowSurface() override;

    MOCK_CONST_METHOD0(getNativeWindow, sp<ANativeWindow>());
    MOCK_METHOD0(preallocateBuffers, void());
};

} // namespace android::surfaceflinger::mock
