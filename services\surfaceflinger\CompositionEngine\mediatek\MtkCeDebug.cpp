/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2018. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */
#define ATRACE_TAG ATRACE_TAG_GRAPHICS

#include "MtkCeDebug.h"

#include <compositionengine/impl/Output.h>
#include <compositionengine/RenderSurface.h>
#include <renderengine/RenderEngine.h>

namespace android {
namespace compositionengine {

namespace impl {
#ifdef MTK_SF_SMART_COMPOSITION
#define FBTHitHint_RPT_STOP 0
#define FBTHitHint_RPT_START 1
#define FBTHitHint_RPT 2
bool isFBTHitHintReportEnabled() {
    bool enabled = false;
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mFBTHitHint != FBTHitHint_RPT_STOP)) {
        enabled = true;
    }
    return enabled;
}

bool isFBTHitHintReport() {
    bool enabled = false;
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mFBTHitHint == FBTHitHint_RPT)) {
        enabled = true;
    }
    return enabled;
}
}
#endif

#ifdef MTK_SF_DEBUG_SUPPORT
void startLogRepaint(const std::string& debugName)
{
    // doDisplayComposition debug msg
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mLogRepaint)) {
        ALOGD("[composeSurfaces] (%s) +", debugName.c_str());
    }
}

namespace impl {
void Output::setupCtDrawDebugLine() {
    const uint32_t steps = 32;
    static bool enableLineG3D = false;
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mLineG3D != enableLineG3D)) {
        enableLineG3D = SFProperty::getInstance().getPropState()->mLineG3D;
        auto& re = getCompositionEngine().getRenderEngine();
        re.enableDebugLine(enableLineG3D);
    }
    if (CC_UNLIKELY(enableLineG3D)) {
        auto& re = getCompositionEngine().getRenderEngine();
        auto* rs = getRenderSurface();
        if (rs == nullptr) return;
        const auto& outputState = getState();
        uint32_t color = 0;
        //if (outputState.layerStackInternal) {
        //    color = 0xFF0000FF;
        //} else {
            color = 0xFFFF0000;  // can't separate virtual and external
        //}
        re.setDebugLineConfig(outputState.displaySpace.getBounds().getWidth(),
                outputState.displaySpace.getBounds().getHeight(), rs->getPageFlipCount(), color, steps);
    }
}
}; // namespace impl
#endif

}; // namespace compositionengine
}; // namespace android
