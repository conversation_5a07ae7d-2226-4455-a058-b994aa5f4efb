### TransactionTrace Testsuite ###

Hassle free way to test and validate whether a sequence of
transactions will produce the expected front end state(s). Test
runs through all testdata/transactions_trace_*.winscope files,
generates layer states and checks if the states match the
corresponding layer trace in testdata.


#### Run Test ####
`atest transactiontrace_testsuite`


#### Workflow ####
Add transaction traces that resulted in front end bugs along
with the layer trace after fixing the issue. The layer trace
can be generated by using the layertracegenerator tool. The
main goal of this test suite is to add regression tests with
minimal effort.

