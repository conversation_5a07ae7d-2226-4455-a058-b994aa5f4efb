package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "hardware_libhardware_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["hardware_libhardware_license"],
}

cc_library_static {
    name: "multihal",
    vendor: true,
    srcs: [
        "multihal.cpp",
        "SensorEventQueue.cpp",
    ],
    header_libs: [
        "libhardware_headers",
    ],
    shared_libs: [
        "liblog",
        "libcutils",
        "libutils",
        "libdl",
    ],
    export_include_dirs: ["."],
    cflags: [
        "-Wall",
        "-Werror",
    ],
}

cc_test_host {
    name: "sensorstests",
    gtest: false,
    srcs: [
        "SensorEventQueue.cpp",
        "tests/SensorEventQueue_test.cpp",
    ],
    static_libs: [
        "libcutils",
        "libutils",
    ],
    cflags: [
        "-Wall",
        "-Werror",
    ],
}
