package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_native_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_native_license"],
}

cc_test {
    name: "sffakehwc_test",
    defaults: ["surfaceflinger_defaults"],
    test_suites: ["device-tests"],
    srcs: [
        "FakeComposerClient.cpp",
        "FakeComposerService.cpp",
        "FakeComposerUtils.cpp",
        "SFFakeHwc_test.cpp",
    ],
    require_root: true,
    shared_libs: [
        "android.hardware.graphics.composer@2.1",
        "android.hardware.graphics.composer@2.2",
        "android.hardware.graphics.composer@2.3",
        "android.hardware.graphics.composer@2.4",
        "android.hardware.graphics.composer3-V1-ndk",
        "android.hardware.graphics.mapper@2.0",
        "android.hardware.graphics.mapper@3.0",
        "android.hardware.graphics.mapper@4.0",
        "android.hardware.power@1.3",
        "android.hardware.power-V2-cpp",
        "libbase",
        "libbinder",
        "libbinder_ndk",
        "libcutils",
        "libfmq",
        "libgui",
        "libhidlbase",
        "liblayers_proto",
        "liblog",
        "libnativewindow",
        "libsync",
        "libtimestats",
        "libui",
        "libutils",
    ],
    static_libs: [
        "android.hardware.graphics.composer@2.1-resources",
        "libaidlcommonsupport",
        "libcompositionengine",
        "libgmock",
        "libperfetto_client_experimental",
        "librenderengine",
        "libtrace_proto",
        "libaidlcommonsupport",
    ],
    header_libs: [
        "android.hardware.graphics.composer@2.4-command-buffer",
        "android.hardware.graphics.composer@2.4-hal",
        "android.hardware.graphics.composer3-command-buffer",
        "libsurfaceflinger_headers",
    ],
}
