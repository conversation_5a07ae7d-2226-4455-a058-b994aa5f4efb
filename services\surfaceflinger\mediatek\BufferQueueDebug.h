#ifndef ANDROID_GUI_BUFFERQUEUEDEBG_H
#define ANDROID_GUI_BUFFERQUEUEDEBG_H

#include <utils/RefBase.h>

namespace android {
// ----------------------------------------------------------------------------
class String8;
class BufferQueueDumpAPI;
struct BufferQueueDebug {
    // debug target BQ info
    int32_t mId;
    int mConnectedApi;
    std::string mConsumerName;
    std::string mMiniConusmerName;

    // process info
    int32_t mPid;
    int32_t mProducerPid;
    int32_t mConsumerPid;
    std::string mProducerProcName;
    std::string mConsumerProcName;

    // if debug line enabled
    bool mLine;
    // debug line count
    uint32_t mLineCnt;

    BufferQueueDumpAPI* mDump;

    BufferQueueDebug();
    virtual ~BufferQueueDebug();
    void onConstructor(const std::string& consumerName);
    void onDestructor();
    void onDump(String8 &result, const String8& prefix) const;
    void onFreeBufferLocked(const int slot);
    void onConsumerConnect();
    void onConsumerDisconnectHead();
    void onConsumerDisconnectTail();
    void onSetConsumerName(const std::string& consumerName);
    void onProducerConnect(const int api);
    void onProducerDisconnect();
    void onAcquire(
            const int buf,
            const sp<GraphicBuffer>& gb,
            const sp<Fence>& fence,
            const int64_t& timestamp,
            const uint32_t& transform,
            const BufferItem* const buffer);
    void onRelease(const int buf);
    void checkFps();
    void setIonInfo(const sp<GraphicBuffer>& gb);
    void toggleDrawDebugLine();
};

// ----------------------------------------------------------------------------
}; // namespace android
#endif // ANDROID_GUI_BUFFERQUEUEDEBG_H