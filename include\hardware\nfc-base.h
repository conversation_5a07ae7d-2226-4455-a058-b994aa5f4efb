// This file is autogenerated by hidl-gen. Do not edit manually.
// Source: android.hardware.nfc@1.0
// Location: hardware/interfaces/nfc/1.0/

#ifndef HIDL_GENERATED_ANDROID_HARDWARE_NFC_V1_0_EXPORTED_CONSTANTS_H_
#define HIDL_GENERATED_ANDROID_HARDWARE_NFC_V1_0_EXPORTED_CONSTANTS_H_

#ifdef __cplusplus
extern "C" {
#endif

enum {
    HAL_NFC_OPEN_CPLT_EVT = 0u,
    HAL_NFC_CLOSE_CPLT_EVT = 1u,
    HAL_NFC_POST_INIT_CPLT_EVT = 2u,
    HAL_NFC_PRE_DISCOVER_CPLT_EVT = 3u,
    HAL_NFC_REQUEST_CONTROL_EVT = 4u,
    HAL_NFC_RELEASE_CONTROL_EVT = 5u,
    HAL_NFC_ERROR_EVT = 6u,
};

enum {
    HAL_NFC_STATUS_OK = 0u,
    H<PERSON>_NFC_STATUS_FAILED = 1u,
    HAL_NFC_STATUS_ERR_TRANSPORT = 2u,
    HAL_NFC_STATUS_ERR_CMD_TIMEOUT = 3u,
    HAL_NFC_STATUS_REFUSED = 4u,
};

#ifdef __cplusplus
}
#endif

#endif  // HIDL_GENERATED_ANDROID_HARDWARE_NFC_V1_0_EXPORTED_CONSTANTS_H_
