#include "SpiDisplaySurface.h"
#include <log/log.h>
#include <EGL/egl.h>
#include <EGL/eglext.h>

namespace android {

SpiDisplaySurface::SpiDisplaySurface(uint32_t width, uint32_t height)
    : mWidth(width), mHeight(height), mClientTargetAcquireFence(Fence::NO_FENCE) {
    setupBufferQueue();
}

SpiDisplaySurface::~SpiDisplaySurface() {
    // 清理资源
}

status_t SpiDisplaySurface::setupBufferQueue() {
    // 创建 BufferQueue
    BufferQueue::createBufferQueue(&mProducer, &mConsumer, false);
    
    // 配置 consumer
    mConsumer->setConsumerName(String8("SPI Display Surface"));
    mConsumer->setDefaultBufferSize(mWidth, mHeight);
    mConsumer->setDefaultBufferFormat(HAL_PIXEL_FORMAT_RGB_565);
    
    return NO_ERROR;
}

status_t SpiDisplaySurface::beginFrame(bool mustRecompose) {
    (void)mustRecompose;  // 标记未使用的参数
    return NO_ERROR;
}

status_t SpiDisplaySurface::prepareFrame(CompositionType compositionType) {
    (void)compositionType;  // 标记未使用的参数
    return NO_ERROR;
}

status_t SpiDisplaySurface::advanceFrame() {
    // 获取最新的 buffer 并发送到 SPI
    BufferItem item;
    status_t result = mConsumer->acquireBuffer(&item, 0);
    if (result == NO_ERROR) {
        sendFrameToSpi(item.mGraphicBuffer);
        
        // 正确的 releaseBuffer 调用 - 需要5个参数
        mConsumer->releaseBuffer(item.mSlot, 
                                item.mFrameNumber, 
                                EGL_NO_DISPLAY, 
                                EGL_NO_SYNC_KHR, 
                                Fence::NO_FENCE);
    }
    return NO_ERROR;
}

void SpiDisplaySurface::onFrameCommitted() {
    // 帧提交后的处理
}

void SpiDisplaySurface::dumpAsString(String8& result) const {
    result.appendFormat("SpiDisplaySurface: %dx%d\n", mWidth, mHeight);
}

void SpiDisplaySurface::resizeBuffers(const ui::Size& size) {
    mWidth = static_cast<uint32_t>(size.width);
    mHeight = static_cast<uint32_t>(size.height);
    mConsumer->setDefaultBufferSize(mWidth, mHeight);
}

const sp<Fence>& SpiDisplaySurface::getClientTargetAcquireFence() const {
    return mClientTargetAcquireFence;
}

void SpiDisplaySurface::sendFrameToSpi(const sp<GraphicBuffer>& buffer) {
    if (!buffer) return;
    
    void* data = nullptr;
    status_t result = buffer->lock(GRALLOC_USAGE_SW_READ_OFTEN, &data);
    
    if (result == NO_ERROR && data != nullptr) {
        // 打开 SPI 设备
        int fd = open("/dev/spi_lcd", O_WRONLY);
        if (fd >= 0) {
            // 计算数据大小 (RGB565, 2 bytes per pixel)
            size_t dataSize = mWidth * mHeight * 2;
            
            // 写入到 SPI 设备
            ssize_t written = write(fd, data, dataSize);
            if (written != static_cast<ssize_t>(dataSize)) {
                ALOGE("Failed to write complete frame to SPI device: %zd/%zu", written, dataSize);
            }
            
            close(fd);
        } else {
            ALOGE("Failed to open SPI device: %s", strerror(errno));
        }
        
        buffer->unlock();
    }
}

} // namespace android