/*
 * Copyright 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <chrono>

namespace android {

// Abstract interface for timekeeping which can be injected for unit tests.
class Clock {
public:
    Clock() = default;
    virtual ~Clock() = default;

    // Returns the current time
    virtual std::chrono::steady_clock::time_point now() const = 0;
};

class SteadyClock : public Clock {
public:
    virtual ~SteadyClock() = default;

    std::chrono::steady_clock::time_point now() const override {
        return std::chrono::steady_clock::now();
    }
};

} // namespace android