// Copyright (C) 2011 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// The default audio HAL module, which is a stub, that is loaded if no other
// device specific modules are present. The exact load order can be seen in
// libhardware/hardware.c
//
// The format of the name is audio.<type>.<hardware/etc>.so where the only
// required type is 'primary'. Other possibilites are 'a2dp', 'usb', etc.
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "hardware_libhardware_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["hardware_libhardware_license"],
}

cc_library_shared {
    name: "audio.primary.default",
    relative_install_path: "hw",
    proprietary: true,
    srcs: ["audio_hw.c"],
    header_libs: ["libhardware_headers"],
    shared_libs: [
        "liblog",
    ],
    cflags: ["-Wall", "-Werror", "-Wno-unused-parameter"],
}

// The stub audio HAL module, identical to the default audio hal, but with
// different name to be loaded concurrently with other audio HALs if necessary.
// This can also be used as skeleton for new implementations
//
// The format of the name is audio.<type>.<hardware/etc>.so where the only
// required type is 'primary'. Other possibilites are 'a2dp', 'usb', etc.
cc_library_shared {
    name: "audio.stub.default",
    relative_install_path: "hw",
    proprietary: true,
    srcs: ["audio_hw.c"],
    header_libs: ["libhardware_headers"],
    shared_libs: [
        "liblog",
    ],
    cflags: ["-Wall", "-Werror", "-Wno-unused-parameter"],
}

// The stub audio policy HAL module that can be used as a skeleton for
// new implementations.
cc_library_shared {
    name: "audio_policy.stub",
    relative_install_path: "hw",
    proprietary: true,
    srcs: ["audio_policy.c"],
    header_libs: ["libhardware_headers"],
    shared_libs: [
        "liblog",
    ],
    cflags: ["-Wall", "-Werror", "-Wno-unused-parameter"],
}
