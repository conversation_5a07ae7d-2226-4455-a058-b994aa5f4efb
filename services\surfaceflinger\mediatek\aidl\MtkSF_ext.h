/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifdef MTK_SF_AIDL
#pragma once

#include <aidl/vendor/mediatek/framework/mtksf_ext/BnMtkSF_ext.h>

#include <android-base/logging.h>
#include <android/binder_manager.h>
#include <android/binder_process.h>
#include "SurfaceFlinger.h"

namespace aidl {
namespace vendor {
namespace mediatek {
namespace framework {
namespace mtksf_ext {

class MtkSF_ext : public BnMtkSF_ext {
    ndk::ScopedAStatus setDisplayDejitterConfig(int64_t fps, int64_t pbc, bool* result) override;
private:
    ::android::sp<::android::SurfaceFlinger> m<PERSON><PERSON>;

public:
    void setFlinger(::android::SurfaceFlinger* flinger);

};

}  // namespace mtksf_ext
}  // namespace framework
}  // namespace mediatek
}  // namespace vendor
}  // namespace aidl
#endif
