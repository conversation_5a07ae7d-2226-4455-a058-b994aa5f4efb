/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2010. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */

//#define LOG_NDEBUG 0
#define ATRACE_TAG ATRACE_TAG_GRAPHICS

#include "SurfaceFlinger.h"
#include "mediatek/SFProperty.h"
#include <cutils/properties.h>

#ifdef MTK_SF_DEBUG_SUPPORT
#include <gui/LayerDebugInfo.h>
#include "Layer.h"
#endif

#ifdef MTK_SF_HWC_VDS_SUPPORT
// This name will send form WifiDisplayAdapter.java
// function name "addDisplayDeviceLocked" to create WFD
#define WIFI_DISPLAY_PATTERN "isWifiDpyForHWC"
#endif

#ifdef MTK_COMPOSER_EXT
#include <composer_ext_intf/client_interface.h>
#endif

#if ((defined MTK_SF_CPU_POLICY) || (defined MTK_SF_CPU_POLICY_FOR_LEGACY))
#include "mediatek/SfCpuPolicyAdapter/SfCpuPolicyAdapter.h"
#endif

#ifdef MTK_SF_MSYNC
#include "mediatek/MSync/MSyncSfApi.h"
#endif

#ifdef MTK_SF_SCHEDULE_DELAY
#include <ui/DisplayStatInfo.h>
#include "Scheduler/VsyncConfiguration.h"
#endif

#ifdef MTK_SF_UPDATE_DISPLAY_CAP
#include <android-base/properties.h>
#endif

#ifdef MTK_SF_KICK_IDLE
#include "mediatek/KickIdleHelper.h"
#endif

namespace android {

#ifdef MTK_SF_DEBUG_SUPPORT
void SurfaceFlinger::mtkDump(std::string& result) {
    // for run-time enable property
    SFProperty::getInstance().setMTKProperties(result);
}

void SurfaceFlinger::mtkDumpLayerDebug(std::string& result) const {
    ALOGI("mtkDumpLayerDebug");
    for (const auto& [token, display] : mDisplays) {
        const DisplayDevice* disp_ref = display.get();
        mDrawingState.traverseInZOrder([&](Layer* layer) {
            LayerDebugInfo ldi = layer->getLayerDebugInfo(disp_ref);
            base::StringAppendF(&result, "%s", to_string(ldi).c_str());});
    }
}

status_t SurfaceFlinger::getProcessName(int pid, std::string& name) {
    FILE *fp = fopen(String8::format("/proc/%d/cmdline", pid), "r");
    if (fp != NULL) {
        const size_t size = 64;
        char proc_name[size] = {0};
        char* result = fgets(proc_name, size - 1, fp);
        int ret = fclose(fp);
        if (CC_UNLIKELY(ret != 0)) {
            ALOGE("%s(), fclose fail", __FUNCTION__);
        }
        if (CC_LIKELY(result != nullptr)) {
            name = proc_name;
            return NO_ERROR;
        }
    }

    return INVALID_OPERATION;
}
#endif

#ifdef MTK_SF_HWC_VDS_SUPPORT
bool SurfaceFlinger::specificKindDisplay(const std::string& dpyName) const {
    if (mOvlOnlyForWifiDisplay == false) {
        return false;
    }

    // For Wifi display
    if (std::string::npos != dpyName.find(WIFI_DISPLAY_PATTERN)) {
        ALOGI("%s dpyName:%s is WIFI display", __func__, dpyName.c_str());
        return true;
    } else {
        ALOGI("%s dpyName:%s is not WIFI display", __func__, dpyName.c_str());
    }
    return false;
}
#endif
#ifdef MTK_VDS_HDCP
void SurfaceFlinger::setNextDisplayUsage(bool isWFD, bool isSecure) {
    ALOGV("setNextDisplayUsage %d, %d", (isWFD?1:0), (isSecure?1:0));
#ifdef MTK_COMPOSER_EXT
    if (mMtkComposerExtIntf) {
        uint32_t vdsUsage = static_cast<uint32_t>(ComposerExt::DisplayUsage::kUnknown);
        if (isWFD) vdsUsage |= static_cast<uint32_t>(ComposerExt::DisplayUsage::kIsWFD);
        if (isSecure) vdsUsage |= static_cast<uint32_t>(ComposerExt::DisplayUsage::kIsSecure);
        mMtkComposerExtIntf->setNextDisplayUsage(static_cast<ComposerExt::DisplayUsage>(vdsUsage));
        ALOGI("mMtkComposerExtIntf->setNextDisplayUsage(%" PRIu32 ")", vdsUsage);
    } else {
        ALOGI("mMtkComposerExtIntf null");
    }
#endif
}
#endif
#ifdef MTK_ATRACE_PRESENT_FENCE
class FenceMonitor {
public:
    explicit FenceMonitor(const char* name) : mName(name) {
        std::thread thread(&FenceMonitor::loop, this);
        pthread_setname_np(thread.native_handle(), mName);
        thread.detach();
    }

    void queueFence(const sp<Fence>& fence) {
        char message[64];

        std::lock_guard<std::mutex> lock(mMutex);
        if (fence->getSignalTime() != Fence::SIGNAL_TIME_PENDING) {
            snprintf(message, sizeof(message), "%s fence %d has signaled", mName, fence->get());
            ATRACE_NAME(message);
            // Need an increment on both to make the trace number correct.
            return;
        }
        snprintf(message, sizeof(message), "Trace %s fence %d", mName, fence->get());
        ATRACE_NAME(message);

        mQueue.push_back(fence);
        mCondition.notify_one();
    }

private:
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wmissing-noreturn"
    void loop() {
        struct sched_attr {
            uint32_t size;
            uint32_t sched_policy;
            uint64_t sched_flags;
            int32_t sched_nice;
            uint32_t sched_priority;
            uint64_t sched_runtime;
            uint64_t sched_deadline;
            uint64_t sched_period;
            uint32_t sched_util_min;
            uint32_t sched_util_max;
        };

        sched_attr attr = {};
        attr.size = sizeof(attr);

        attr.sched_flags = (SCHED_FLAG_KEEP_ALL | SCHED_FLAG_UTIL_CLAMP);
        attr.sched_util_min =  0;
        attr.sched_util_max = 1024;

        syscall(__NR_sched_setattr, 0, &attr, 0);

        while (true) {
            threadLoop();
        }
    }
#pragma clang diagnostic pop

    void threadLoop() {
        sp<Fence> fence;
        {
            std::unique_lock<std::mutex> lock(mMutex);
            while (mQueue.empty()) {
                mCondition.wait(lock);
            }
            fence = mQueue[0];
        }
        {
            char message[64];
            snprintf(message, sizeof(message), "waiting for %s %d", mName, fence->get());
            ATRACE_NAME(message);

            status_t result = fence->waitForever(message);
            if (result != OK) {
                ALOGE("Error waiting for fence: %d", result);
            }
            snprintf(message, sizeof(message), "%s, %" PRIu64 ", %" PRIu64,
                    mName, systemTime(), fence->getSignalTime());
            ATRACE_NAME(message);
        }
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mQueue.pop_front();
        }
    }

    const char* mName;
    std::deque<sp<Fence>> mQueue;
    std::condition_variable mCondition;
    std::mutex mMutex;
};

void SurfaceFlinger::trackPresentFence(const sp<Fence>& fence) {
    static FenceMonitor presentFenceMonitor("presentFence");
    presentFenceMonitor.queueFence(fence);
}

void SurfaceFlinger::trackScreenCaptureFence(const sp<Fence>& fence) {
    static FenceMonitor trackScreenCaptureFence("screenCaptureFence");
    trackScreenCaptureFence.queueFence(fence);
}

void SurfaceFlinger::trackWfdPresentFence(const sp<Fence>& fence) {
    static FenceMonitor trackWfdPresentFence("WFD-Present");
    trackWfdPresentFence.queueFence(fence);
}
#endif

#ifdef MTK_SF_HINT_DISPLAY_INFO
void SurfaceFlinger::hintDisplayInfo(const int& policy, const bool& enable)
{
    switch(policy) {
        case Hwc2::impl::PowerAdvisor::MULTI_DISPLAY: {
            const auto defaultdisplay = getDefaultDisplayDeviceLocked();
            if (defaultdisplay) {
                int32_t fps =
                    static_cast<int32_t>(defaultdisplay->refreshRateConfigs().getActiveMode()->getFps().getIntValue());
                mPowerAdvisor->hintMultiDisplay(enable, fps);
            }
            break;
        }
    }
}
#endif

#ifdef MTK_COMPOSER_EXT
class MtkComposerExtCallbackHandler : public ::ComposerExt::ConfigCallback {
public:
    MtkComposerExtCallbackHandler(SurfaceFlinger& flinger)
          : mFlinger(flinger) {
    }
    void NotifyTestCallback(bool value) {
        ALOGI("NotifyTestCallback, %p, value %d", &mFlinger, value);
    }
    void NotifyHwcHwBinderTidCallback(int tid) {
        ALOGI("NotifyHwcHwBinderTidCallback, %p, tid %d", &mFlinger, tid);
#if ((defined MTK_SF_CPU_POLICY) || (defined MTK_SF_CPU_POLICY_FOR_LEGACY))
        mFlinger.notifyHwcHwbinderTid(tid);
#endif
    }
    void NotifyHwcComposition(ComposerExt::HwcCompositionStruct composition) {
        ALOGI("%s(), id %" PRIu64 ", mode %d", __FUNCTION__, composition.disp_id, composition.composition_mode);
#ifdef MTK_DYNAMIC_DURATION
        mFlinger.setDecoupleMode(static_cast<uint64_t>(composition.disp_id)
                , static_cast<int>(composition.composition_mode) > 0 ? true : false);
#endif
    }
    void NotifyHwcDisplayCapability(ComposerExt::HwcDispCapabilityStruct capability) {
        ALOGI("%s(), id %" PRIu64 ", cap %d, state %s", __FUNCTION__,
            capability.disp_id, capability.capability, capability.add_capability?"true":"false");
    }
private:
    SurfaceFlinger& mFlinger;
};

MtkComposerExtCallbackHandler* mMtkComposerExtCallbackHandler = nullptr;

void SurfaceFlinger::initMtkComposerExt() {
    mMtkComposerExtCallbackHandler = new MtkComposerExtCallbackHandler(*this);
    int ret = ::ComposerExt::ClientInterface::create("SurfaceFlinger" + std::to_string(0),
                                                     mMtkComposerExtCallbackHandler,
                                                     &mMtkComposerExtIntf);
    if (ret || !mMtkComposerExtIntf) {
        ALOGE("ComposerExt HIDL not present\n");
        mMtkComposerExtIntf = nullptr;
    }
#ifdef MTK_SF_KICK_IDLE
    KickIdleHelper::getInstance().setComposerExtInf((void*) mMtkComposerExtIntf);
#endif
}
#endif

#ifdef MTK_COMPOSER_EXT
#if ((defined MTK_SF_CPU_POLICY) || (defined MTK_SF_CPU_POLICY_FOR_LEGACY))
void SurfaceFlinger::notifyHwcHwbinderTid(const int& tid) {
    if (mSfCpuPolicy) {
        mSfCpuPolicy->notifyHwcHwbinderTid(tid);
    }
}
#endif
#endif

#if ((defined MTK_SF_CPU_POLICY) || (defined MTK_SF_CPU_POLICY_FOR_LEGACY))
void SurfaceFlinger::notifyVpLpEnable(bool enable) {
    if (mSfCpuPolicy) {
        mSfCpuPolicy->notifyVpLpEnable(enable);
    }
}

void SurfaceFlinger::notifyLayerConnect(const void * token, const std::string& name){
    if (mSfCpuPolicy) {
        mSfCpuPolicy->notifyLayerConnect(token, name);
    }
}

void SurfaceFlinger::notifyLayerDisconnect(const void * token) {
    if (mSfCpuPolicy) {
        mSfCpuPolicy->notifyLayerDisconnect(token);
    }
}

void SurfaceFlinger::notifyLayerSetBuffer(const void * token, const sp<GraphicBuffer>& buffer){
    if (mSfCpuPolicy) {
        mSfCpuPolicy->notifyLayerSetBuffer(token, buffer);
    }
}
#endif

#ifdef MTK_SF_MSYNC
bool SurfaceFlinger::isMsyncOn() const {
    return mMSyncSfApi && mMSyncSfApi->isOn();
}
#endif

#ifdef MTK_DYNAMIC_DURATION
void SurfaceFlinger::initDecouple() {
    char value[PROPERTY_VALUE_MAX];
    property_get("vendor.debug.sf.dynamic_duration.switch", value, "0");
    int decoupleModeSw = atoi(value);
    mDecoupleModeSwitch = decoupleModeSw > 0 ? true : false;
    mActiveDisplayId = INVALID_ACTIVE_DISPLAY_INDEX;
}
bool SurfaceFlinger::duringTransaction(size_t i) {
    return 0 < mDispMML[i].transactionFrames;
}
void SurfaceFlinger::decreTransactionFrames() {
    for (size_t i = 0; i < mDispMML.size(); i++) {
        if (mDispMML[i].transactionFrames > 0) {
            mDispMML[i].transactionFrames--;
        }
    }
}
bool SurfaceFlinger::getDecoupleModeLocked(size_t i) {
    if (duringTransaction(i)) {
        // duration transaction
        return mDispMML[i].transactionMode && mDecoupleModeSwitch;
    }
    return mDispMML[i].decoupleMode && mDecoupleModeSwitch;
}
size_t SurfaceFlinger::getActiveDisplayIndex() {
    if (mActiveDisplayIndex == INVALID_ACTIVE_DISPLAY_INDEX ||
        mDispMML[mActiveDisplayIndex].displayId != mActiveDisplayId) {
        for (size_t i = 0; i < mDispMML.size(); i++) {
            if (mDispMML[i].displayId == mActiveDisplayId) {
                mActiveDisplayIndex = i;
                break;
            }
        }
    }
    if (mActiveDisplayIndex == INVALID_ACTIVE_DISPLAY_INDEX) {
        ALOGW("mActiveDisplayIndex !valid, return 0 instead");
        mActiveDisplayIndex = 0;
    }
    return mActiveDisplayIndex;
}
bool SurfaceFlinger::getDecoupleModeUpdate() {
    std::lock_guard<std::mutex> lock(mDecoupleModeMutex);
    decreTransactionFrames();
    return getDecoupleModeLocked(getActiveDisplayIndex());
}
void SurfaceFlinger::onNewInternalDisplay(uint64_t id) {
    std::lock_guard<std::mutex> lock(mDecoupleModeMutex);
    for (size_t i = 0; i < mDispMML.size(); i++) {
        if (mDispMML[i].displayId == id) {
            ALOGW("onNewInternalDisplay, already exist %" PRIu64, id);
            return;
        }
    }
    mDispMML.emplace_back(id, false, 0, false);
    ALOGD("onNewInternalDisplay, %" PRIu64, id);
}
void SurfaceFlinger::updateActiveDisplayId(uint64_t id) {
    std::lock_guard<std::mutex> lock(mDecoupleModeMutex);
    mActiveDisplayId = id;

    bool find = false;
    for (size_t i = 0; i < mDispMML.size(); i++) {
        if (mDispMML[i].displayId == mActiveDisplayId) {
            find = true;
            mActiveDisplayIndex = i;
            break;
        }
    }
    if (!find) {
        ALOGW("updateActiveDisplayId !find %" PRIu64, id);
    } else {
        ALOGD("updateActiveDisplayId %" PRIu64, id);
    }
}
void SurfaceFlinger::setDecoupleMode(uint64_t id, bool mode) {
    std::lock_guard<std::mutex> lock(mDecoupleModeMutex);
    bool find = false;
    for (size_t i = 0; i < mDispMML.size(); i++) {
        if (mDispMML[i].displayId == id) {
            find = true;
            if (mDispMML[i].decoupleMode != mode && !duringTransaction(i)) {
                mDispMML[i].transactionFrames = MIN_TRANSACTION_FRAMES;
                mDispMML[i].transactionMode = mode;
            }
            mDispMML[i].decoupleMode = mode;
            break;
        }
    }
    if (find) {
        std::string name = "DecoupleMode_" + std::to_string(id);
        ATRACE_INT(name.c_str(), (mode?1:0));
    }
}
#endif

#ifdef MTK_DISPLAY_DEJITTER
bool SurfaceFlinger::setDisplayDejitterConfig(int64_t fps, int64_t pbc) {
    ALOGD("setDisplayDejitterConfig %" PRId64 ", %" PRId64, fps, pbc);
    if (fps < 0 || pbc < 0) return false;
    Mutex::Autolock lock(mDejitterConfigMutex);
    mDejitterfps = fps;
    mDejitterpbc = pbc;
    return true;
}
#endif
#ifdef MTK_SF_SCHEDULE_DELAY
int64_t SurfaceFlinger::getDelayTimeForTransaction(bool isauto, int64_t desiredTime) {
    int64_t delayed_time = 0;
    if (!isauto) {
        const auto now = systemTime();
#ifdef MTK_DYNAMIC_DURATION
        const auto sfWorkDuration = mDrawingDecoupleMode ?
            mVsyncConfiguration->getCurrentConfigs().decouple.sfWorkDuration :
            mVsyncConfiguration->getCurrentConfigs().late.sfWorkDuration;
#else
        const auto sfWorkDuration = mVsyncConfiguration->getCurrentConfigs().late.sfWorkDuration;
#endif
        const auto vsyncPeriod = mScheduler->getDisplayStatInfo(systemTime()).vsyncPeriod;
        const auto lastScheduledPresentTime = mScheduledPresentTime;
        // we add 2ms to conver slight shift of presentTime
        const auto nextPredictedPresentTime = lastScheduledPresentTime + vsyncPeriod + ms2ns(2);
        const auto delayedWakeUpTime = desiredTime - sfWorkDuration.count();
        delayed_time = delayedWakeUpTime - now;
        if (desiredTime <= nextPredictedPresentTime || // don't delay if it is in next vsync.
            delayed_time > mDelayTransactionThreshold) { // don't delay too much
            delayed_time = 0;
        }
    }
    ATRACE_INT64("delayedTransaction", delayed_time);
    return delayed_time;
}
#endif
#ifdef MTK_SF_UPDATE_DISPLAY_CAP
bool SurfaceFlinger::updateDisplayCapability() {
    static bool supportDsiSwitch = base::GetBoolProperty("ro.vendor.mtk_hwc_dsi_switch", false);
    if (!supportDsiSwitch) return false;
    ATRACE_CALL();
    for (const auto& [token, display] : mDisplays) {
        if (const auto halDispId = HalDisplayId::tryCast(display->getId())) {
            getHwComposer().updateDisplayCapability(halDispId.value());
        } else {
            ALOGE("!halDispId");
        }
    }
    return true;
}
#endif

#ifdef MTK_SF_MSYNC_3
void SurfaceFlinger::changeToForeground() {
#ifdef MTK_SF_CPU_POLICY
    if (SfCpuPolicyAdapter::isEnabled()) {
        ATRACE_NAME("SF: changeToForeground & notifySpeedUpRE");
        SfCpuPolicyAdapter::getInstance(*mFrameTimeline).changeToForeground();
        // 370 is upper bound for uclmap.min perf range 0-1024
        SfCpuPolicyAdapter::getInstance(*mFrameTimeline).notifySpeedUpRE(370);
    }
#endif
}
#endif

#ifdef MTK_SF_KICK_IDLE
void SurfaceFlinger::updateIdleTimer() {
    // return disp-driver formula: 3 * 1000/hz + 1
    const auto vsyncPeriod = mScheduler->getDisplayStatInfo(systemTime()).vsyncPeriod;
    const auto display = getDefaultDisplayDevice();
    const auto refreshRate = display->refreshRateConfigs().getActiveMode()->getFps();
#ifdef MTK_DYNAMIC_DURATION
    const auto vsyncConfig = mDrawingDecoupleMode ?
        mVsyncConfiguration->getConfigsForRefreshRate(refreshRate).decouple :
        mVsyncConfiguration->getConfigsForRefreshRate(refreshRate).late;
#else
    const auto vsyncConfig = mVsyncConfiguration->getConfigsForRefreshRate(refreshRate).late;
#endif
    const nsecs_t sfWorkDuration = (nsecs_t) vsyncConfig.sfWorkDuration.count();

    KickIdleHelper::getInstance().updateIdleTimer(vsyncPeriod, sfWorkDuration);
}
#endif
}; // namespace android
