package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "hardware_libhardware_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["hardware_libhardware_license"],
}

cc_library_static {
    name: "static-hal-check",
    srcs: [
        "struct-size.cpp",
        "struct-offset.cpp",
        "struct-last.cpp",
    ],
    shared_libs: ["libhardware"],
    cflags: [
        "-O0",
        "-Wall",
        "-Werror",
    ],

    include_dirs: ["system/media/camera/include"],
}
