#undef LOG_TAG
#define LOG_TAG "PowerHalWrapper"

#include <dlfcn.h>
#include <utils/Log.h>
#include "DisplayHardware/PowerHalWrapper.h"

namespace android {

#ifdef MTK_SF_HINT_DISPLAY_INFO
PowerHalWrapper& PowerHalWrapper::getInstance() {
    static PowerHalWrapper gInstance;
    return gInstance;
}

PowerHalWrapper::PowerHalWrapper()
{
    mSoHandle = NULL;
    mEnableMultiDisplayMode = NULL;

    mSoHandle = dlopen(LIB_FULL_NAME, RTLD_NOW);
    if (mSoHandle != NULL) {
        void *func = dlsym(mSoHandle, "PowerHal_Wrap_EnableMultiDisplayMode");
        mEnableMultiDisplayMode = reinterpret_cast<enableMultiDisplayMode>(func);
        if (mEnableMultiDisplayMode == NULL) {
            ALOGE("PowerAdvisor::setupPowerHalAPI init fail!");
        }
    } else {
        ALOGE("PowerAdvisor::setupPowerHalAPI dlerror:%s", dlerror());
    }
}

PowerHalWrapper::~PowerHalWrapper()
{
    if (mSoHandle) {
        dlclose(mSoHandle);
    }
}

void PowerHalWrapper::hintMultiDisplay(const int& enable, const int& fps)
{
    if (mEnableMultiDisplayMode) {
        ALOGI("mEnableMultiDisplayMode enable=%d fps=%d", enable, fps);
        mEnableMultiDisplayMode(enable, fps);
    }
}
#endif

} // namespace android
