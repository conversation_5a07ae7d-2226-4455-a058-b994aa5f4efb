/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

//#define LOG_NDEBUG 0
#define LOG_TAG "BoottimeStateDelegate"

#include "boottime_state_delegate.h"

#include <cerrno>
#include <cstring>

#include "common.h"

namespace v4l2_camera_hal {

int BoottimeStateDelegate::GetValue(int64_t* value) {
  struct timespec ts;

  int res = clock_gettime(CLOCK_BOOTTIME, &ts);
  if (res) {
    HAL_LOGE("Failed to get BOOTTIME for state delegate: %d (%s)",
             errno,
             strerror(errno));
    return -errno;
  }
  *value = ts.tv_sec * 1000000000ULL + ts.tv_nsec;

  return 0;
}

}  // namespace v4l2_camera_hal
