/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ANDROID_BIT_UTILS_H_
#define ANDROID_BIT_UTILS_H_

#include <cstdint>

namespace android {

/** Test whether any bits in the interval [start, end) are set in the array. */
bool testBitInRange(const uint8_t arr[], size_t start, size_t end);

}  // namespace android

#endif  // ANDROID_BIT_UTILS_H_
