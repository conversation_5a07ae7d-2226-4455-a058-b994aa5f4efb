/*
 * Copyright 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// TODO(b/129481165): remove the #pragma below and fix conversion issues
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wextra"

// #define LOG_NDEBUG 0
#define ATRACE_TAG ATRACE_TAG_GRAPHICS

#include "LayerInfo.h"

#ifdef MTK_SF_MSYNC_3
#include <android-base/properties.h>
#endif
#include <algorithm>
#include <utility>

#include <cutils/compiler.h>
#include <cutils/trace.h>
#include <ftl/enum.h>

#undef LOG_TAG
#define LOG_TAG "LayerInfo"

namespace android::scheduler {

bool LayerInfo::sTraceEnabled = false;

#ifdef MTK_SF_MSYNC_3
#define NEED_PROMOTE_FPS_SURFACE_VIEW "SurfaceView[tv.danmaku.bili"
#define NEED_PROMOTE_FPS_THRESHOLD_LOW 30
#define NEED_PROMOTE_FPS_THRESHOLD_HIGH 60
static int sDefaultRefreshRate = 60;
static bool sShowContentDetectionDetailLog = false;
static bool sUseNoPerceptionAnimation = false;
static bool isSupportContentDetection() {
    static bool enable = false;
    static bool read = false;
    if (!read) {
        enable = android::base::GetBoolProperty("debug.sf.use_content_detection_for_refresh_rate", false);
        sDefaultRefreshRate = android::base::GetIntProperty("debug.sf.default_refresh_rate", 60);
        sShowContentDetectionDetailLog = android::base::GetBoolProperty("debug.sf.show_content_detection_detail_log", false);
        sUseNoPerceptionAnimation = android::base::GetBoolProperty("debug.sf.use_no_perception_animation", true);
        read = true;
    }
    return enable;
}
static bool isShowCDDetailLog() {
    return sShowContentDetectionDetailLog;
}
static bool isUseNoPerceptionAnimation() {
    return sUseNoPerceptionAnimation;
}
#endif

LayerInfo::LayerInfo(const std::string& name, uid_t ownerUid,
                     LayerHistory::LayerVoteType defaultVote)
      : mName(name),
        mOwnerUid(ownerUid),
        mDefaultVote(defaultVote),
        mLayerVote({defaultVote, Fps()}),
        mRefreshRateHistory(name) {}

void LayerInfo::setLastPresentTime(nsecs_t lastPresentTime, nsecs_t now, LayerUpdateType updateType,
                                   bool pendingModeChange, LayerProps props) {
    lastPresentTime = std::max(lastPresentTime, static_cast<nsecs_t>(0));

    mLastUpdatedTime = std::max(lastPresentTime, now);
#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection()) {
        int oldFps = mLayerProps.setFrameRateVote.rate.getIntValue();
        int newFps = props.setFrameRateVote.rate.getIntValue();
        //ALOGI("%s: %s old fps=%d, new fps=%d", __func__, mName.c_str(), oldFps, newFps);
        if (oldFps > 0 && oldFps < 1000 && newFps == 1000) {
            mTouchScrollModeEndTime = systemTime();
        } else if (oldFps == 1000 && (newFps == 0 || newFps == 1001)) {
            mTouchScrollModeEndTime = 0;
        }
    }
#endif
    mLayerProps = props;
    switch (updateType) {
        case LayerUpdateType::AnimationTX:
            mLastAnimationTime = std::max(lastPresentTime, now);
            break;
        case LayerUpdateType::SetFrameRate:
        case LayerUpdateType::Buffer:
            FrameTimeData frameTime = {.presentTime = lastPresentTime,
                                       .queueTime = mLastUpdatedTime,
                                       .pendingModeChange = pendingModeChange};
            mFrameTimes.push_back(frameTime);
            if (mFrameTimes.size() > HISTORY_SIZE) {
                mFrameTimes.pop_front();
            }
            break;
    }
}

bool LayerInfo::isFrameTimeValid(const FrameTimeData& frameTime) const {
    return frameTime.queueTime >= std::chrono::duration_cast<std::chrono::nanoseconds>(
                                          mFrameTimeValidSince.time_since_epoch())
                                          .count();
}

bool LayerInfo::isFrequent(nsecs_t now) const {
    using fps_approx_ops::operator>=;
    // If we know nothing about this layer we consider it as frequent as it might be the start
    // of an animation.
    if (mFrameTimes.size() < kFrequentLayerWindowSize) {
        return true;
    }
    return getFps(now) >= kMinFpsForFrequentLayer;
}

Fps LayerInfo::getFps(nsecs_t now) const {
    // Find the first active frame
    auto it = mFrameTimes.begin();
    for (; it != mFrameTimes.end(); ++it) {
        if (it->queueTime >= getActiveLayerThreshold(now)) {
            break;
        }
    }

    const auto numFrames = std::distance(it, mFrameTimes.end());
    if (numFrames < kFrequentLayerWindowSize) {
        return Fps();
    }

    // Layer is considered frequent if the average frame rate is higher than the threshold
    const auto totalTime = mFrameTimes.back().queueTime - it->queueTime;
    return Fps::fromPeriodNsecs(totalTime / (numFrames - 1));
}

bool LayerInfo::isAnimating(nsecs_t now) const {
    return mLastAnimationTime >= getActiveLayerThreshold(now);
}

bool LayerInfo::hasEnoughDataForHeuristic() const {
    // The layer had to publish at least HISTORY_SIZE or HISTORY_DURATION of updates
    if (mFrameTimes.size() < 2) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s fewer than 2 frames recorded: %zu", __func__, mName.c_str(), mFrameTimes.size());
        }
#else
        ALOGV("fewer than 2 frames recorded: %zu", mFrameTimes.size());
#endif
        return false;
    }

    if (!isFrameTimeValid(mFrameTimes.front())) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s stale frames still captured", __func__, mName.c_str());
        }
#else
        ALOGV("stale frames still captured");
#endif
        return false;
    }

    const auto totalDuration = mFrameTimes.back().queueTime - mFrameTimes.front().queueTime;
    if (mFrameTimes.size() < HISTORY_SIZE && totalDuration < HISTORY_DURATION.count()) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s not enough frames captured: %zu | %.2f seconds", __func__, mName.c_str(), mFrameTimes.size(),
                totalDuration / 1e9f);
        }
#else
        ALOGV("not enough frames captured: %zu | %.2f seconds", mFrameTimes.size(),
              totalDuration / 1e9f);
#endif
        return false;
    }

    return true;
}

std::optional<nsecs_t> LayerInfo::calculateAverageFrameTime() const {
    // Ignore frames captured during a mode change
    const bool isDuringModeChange =
            std::any_of(mFrameTimes.begin(), mFrameTimes.end(),
                        [](const auto& frame) { return frame.pendingModeChange; });
    if (isDuringModeChange) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s isDuringModeChange, return null", __func__, mName.c_str());
        }
#endif
        return std::nullopt;
    }

    const bool isMissingPresentTime =
            std::any_of(mFrameTimes.begin(), mFrameTimes.end(),
                        [](auto frame) { return frame.presentTime == 0; });
    if (isMissingPresentTime && !mLastRefreshRate.reported.isValid()) {
        // If there are no presentation timestamps and we haven't calculated
        // one in the past then we can't calculate the refresh rate
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s isMissingPresentTime, return null", __func__, mName.c_str());
        }
#endif
        return std::nullopt;
    }

    // Calculate the average frame time based on presentation timestamps. If those
    // doesn't exist, we look at the time the buffer was queued only. We can do that only if
    // we calculated a refresh rate based on presentation timestamps in the past. The reason
    // we look at the queue time is to handle cases where hwui attaches presentation timestamps
    // when implementing render ahead for specific refresh rates. When hwui no longer provides
    // presentation timestamps we look at the queue time to see if the current refresh rate still
    // matches the content.

    auto getFrameTime = isMissingPresentTime ? [](FrameTimeData data) { return data.queueTime; }
                                             : [](FrameTimeData data) { return data.presentTime; };

    nsecs_t totalDeltas = 0;
    int numDeltas = 0;
    auto prevFrame = mFrameTimes.begin();
    for (auto it = mFrameTimes.begin() + 1; it != mFrameTimes.end(); ++it) {
        const auto currDelta = getFrameTime(*it) - getFrameTime(*prevFrame);
        if (currDelta < kMinPeriodBetweenFrames) {
            // Skip this frame, but count the delta into the next frame
            continue;
        }

        prevFrame = it;

        if (currDelta > kMaxPeriodBetweenFrames) {
            // Skip this frame and the current delta.
            continue;
        }

        totalDeltas += currDelta;
        numDeltas++;
    }

    if (numDeltas == 0) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s numDeltas == 0, return null", __func__, mName.c_str());
        }
#endif
        return std::nullopt;
    }

    const auto averageFrameTime = static_cast<double>(totalDeltas) / static_cast<double>(numDeltas);
#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection() && isShowCDDetailLog()) {
        ALOGI("%s: %s averageFrameTime=%" PRId64, __func__, mName.c_str(), static_cast<nsecs_t>(averageFrameTime));
    }
#endif
    return static_cast<nsecs_t>(averageFrameTime);
}

std::optional<Fps> LayerInfo::calculateRefreshRateIfPossible(
        const RefreshRateConfigs& refreshRateConfigs, nsecs_t now) {
#ifdef MTK_SF_MSYNC_3
    static constexpr float MARGIN = 1.0f; // 1Hz
    bool bCheckDefaultDistance = false;
    if (isSupportContentDetection() && mName.find("Wallpaper") != std::string::npos) {
        bCheckDefaultDistance = true;
    }
#else
    static constexpr float MARGIN = 1.0f; // 1Hz
#endif
    if (!hasEnoughDataForHeuristic()) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s Not enough data", __func__, mName.c_str());
        }
#else
        ALOGV("Not enough data");
#endif
        return std::nullopt;
    }

    if (const auto averageFrameTime = calculateAverageFrameTime()) {
        const auto refreshRate = Fps::fromPeriodNsecs(*averageFrameTime);
        const bool refreshRateConsistent = mRefreshRateHistory.add(refreshRate, now);
        if (refreshRateConsistent) {
#ifdef MTK_SF_MSYNC_3
            const auto knownRefreshRate = refreshRateConfigs.findClosestKnownFrameRate(refreshRate, bCheckDefaultDistance);
#else
            const auto knownRefreshRate = refreshRateConfigs.findClosestKnownFrameRate(refreshRate);
#endif
            using fps_approx_ops::operator!=;

            // To avoid oscillation, use the last calculated refresh rate if it is close enough.
            if (std::abs(mLastRefreshRate.calculated.getValue() - refreshRate.getValue()) >
                        MARGIN &&
                mLastRefreshRate.reported != knownRefreshRate) {
                mLastRefreshRate.calculated = refreshRate;
                mLastRefreshRate.reported = knownRefreshRate;
            }

#ifdef MTK_SF_MSYNC_3
            if (isSupportContentDetection() && isShowCDDetailLog()) {
                ALOGI("%s: %s %s rounded to nearest known frame rate %s", __func__, mName.c_str(),
                    to_string(refreshRate).c_str(), to_string(mLastRefreshRate.reported).c_str());
            }
#else
            ALOGV("%s %s rounded to nearest known frame rate %s", mName.c_str(),
                  to_string(refreshRate).c_str(), to_string(mLastRefreshRate.reported).c_str());
#endif
        } else {
#ifdef MTK_SF_MSYNC_3
            if (isSupportContentDetection() && isShowCDDetailLog()) {
                ALOGI("%s: %s Not stable (%s) returning last known frame rate %s", __func__, mName.c_str(),
                    to_string(refreshRate).c_str(), to_string(mLastRefreshRate.reported).c_str());
            }
#else
            ALOGV("%s Not stable (%s) returning last known frame rate %s", mName.c_str(),
                  to_string(refreshRate).c_str(), to_string(mLastRefreshRate.reported).c_str());
#endif
        }
    }

    return mLastRefreshRate.reported.isValid() ? std::make_optional(mLastRefreshRate.reported)
                                               : std::nullopt;
}

#ifdef MTK_SF_MSYNC_3
bool LayerInfo::hasSpecificFps(int fps, int *outFps) {
    if (!isUseNoPerceptionAnimation()) {
        return false;
    }
    if (fps == REFRESH_RATE_SET_DEFAULT) {
        *outFps = sDefaultRefreshRate;
        return true;
    } else if (fps > REFRESH_RATE_SET_DEFAULT_BY_AP) {
        *outFps = fps - REFRESH_RATE_SET_DEFAULT_BY_AP;
        return true;
    }
    return false;
}
#endif

LayerInfo::LayerVote LayerInfo::getRefreshRateVote(const RefreshRateConfigs& refreshRateConfigs,
                                                   nsecs_t now) {
#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection() && mTouchScrollModeEndTime > 0) {
        auto duration = systemTime() - mTouchScrollModeEndTime;
        if (ns2ms(duration) < 1800) {
            if (isShowCDDetailLog()) {
                ALOGI("%s: %s NoChange (scroll mode end elapsed %ld ms)", __func__,
                        mName.c_str(), long(ns2ms(duration)));
            }
            return {LayerHistory::LayerVoteType::NoChange, Fps()};
        } else {
            mTouchScrollModeEndTime = 0;
        }
    }
#endif

    if (mLayerVote.type != LayerHistory::LayerVoteType::Heuristic) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()
                && mLayerVote.type != LayerHistory::LayerVoteType::NoVote) {
            ALOGI("%s: %s voted %d ", __func__, mName.c_str(), static_cast<int>(mLayerVote.type));
        }
#else
        ALOGV("%s voted %d ", mName.c_str(), static_cast<int>(mLayerVote.type));
#endif
        return mLayerVote;
    }

    if (isAnimating(now)) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection() && isShowCDDetailLog()) {
            ALOGI("%s: %s is animating, Max", __func__, mName.c_str());
        }
#else
        ALOGV("%s is animating", mName.c_str());
#endif
        mLastRefreshRate.animatingOrInfrequent = true;
        return {LayerHistory::LayerVoteType::Max, Fps()};
    }

    if (!isFrequent(now)) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection()) {
            int outFps = 0;
            if (hasSpecificFps(mLayerVote.fps.getIntValue(), &outFps)) {
                if (isShowCDDetailLog()) {
                    ALOGI("%s: %s is infrequent, Heuristic_Infrequent", __func__, mName.c_str());
                }
                mLastRefreshRate.animatingOrInfrequent = true;
                return {LayerHistory::LayerVoteType::Heuristic_Infrequent, Fps::fromValue(outFps)};
            } else if (isShowCDDetailLog()) {
                ALOGI("%s: %s is infrequent, Min", __func__, mName.c_str());
            }
        }
#else
        ALOGV("%s is infrequent", mName.c_str());
#endif
        mLastRefreshRate.animatingOrInfrequent = true;
        // Infrequent layers vote for mininal refresh rate for
        // battery saving purposes and also to prevent b/135718869.
        return {LayerHistory::LayerVoteType::Min, Fps()};
    }

    // If the layer was previously tagged as animating or infrequent, we clear
    // the history as it is likely the layer just changed its behavior
    // and we should not look at stale data
    if (mLastRefreshRate.animatingOrInfrequent) {
        clearHistory(now);
    }

#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection() && !hasEnoughDataForHeuristic()) {
        int outFps = 0;
        if (hasSpecificFps(mLayerVote.fps.getIntValue(), &outFps)) {
            if (isShowCDDetailLog()) {
                ALOGI("%s: %s not enough data, Heuristic_Infrequent", __func__, mName.c_str());
            }
            return {LayerHistory::LayerVoteType::Heuristic_Infrequent, Fps::fromValue(outFps)};
        }
    }
#endif

    auto refreshRate = calculateRefreshRateIfPossible(refreshRateConfigs, now);
    if (refreshRate.has_value()) {
#ifdef MTK_SF_MSYNC_3
        if (isSupportContentDetection()) {
            // some app need high refresh rate to reach avg fps
            if (mName.compare(0, strlen(NEED_PROMOTE_FPS_SURFACE_VIEW), NEED_PROMOTE_FPS_SURFACE_VIEW) == 0) {
                int outFps = 0;
                if (refreshRate.value().getIntValue() <= NEED_PROMOTE_FPS_THRESHOLD_LOW) {
                    outFps = NEED_PROMOTE_FPS_THRESHOLD_LOW * 2;
                    if (isShowCDDetailLog()) {
                        ALOGI("%s: %s promote refresh rate to %d, Heuristic", __func__, mName.c_str(), outFps);
                    }
                    return {LayerHistory::LayerVoteType::Heuristic, Fps::fromValue(outFps)};
                } /*else {
                    outFps = NEED_PROMOTE_FPS_THRESHOLD_HIGH * 2;
                    ALOGI("%s: %s promote refresh rate to %d, Exact", __func__, mName.c_str(), outFps);
                    return {LayerHistory::LayerVoteType::ExplicitExact, Fps::fromValue(outFps)};
                }*/
            } else if (isShowCDDetailLog()) {
                ALOGI("%s: %s calculated refresh rate: %s, Heuristic", __func__, mName.c_str(), to_string(*refreshRate).c_str());
            }
        }
#else
        ALOGV("%s calculated refresh rate: %s", mName.c_str(), to_string(*refreshRate).c_str());
#endif
        return {LayerHistory::LayerVoteType::Heuristic, refreshRate.value()};
    }

#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection()) {
        int outFps = 0;
        if (hasSpecificFps(mLayerVote.fps.getIntValue(), &outFps)) {
            if (isShowCDDetailLog()) {
                ALOGI("%s: %s Heuristic (can't resolve refresh rate), default=%d", __func__, mName.c_str(), outFps);
            }
            return {LayerHistory::LayerVoteType::Heuristic, Fps::fromValue(outFps)};
        } else if (isShowCDDetailLog()) {
            ALOGI("%s: %s Max (can't resolve refresh rate)", __func__, mName.c_str());
        }
    }
#else
    ALOGV("%s Max (can't resolve refresh rate)", mName.c_str());
#endif
    return {LayerHistory::LayerVoteType::Max, Fps()};
}

const char* LayerInfo::getTraceTag(LayerHistory::LayerVoteType type) const {
    if (mTraceTags.count(type) == 0) {
        auto tag = "LFPS " + mName + " " + ftl::enum_string(type);
        mTraceTags.emplace(type, std::move(tag));
    }

    return mTraceTags.at(type).c_str();
}

LayerInfo::RefreshRateHistory::HeuristicTraceTagData
LayerInfo::RefreshRateHistory::makeHeuristicTraceTagData() const {
    const std::string prefix = "LFPS ";
    const std::string suffix = "Heuristic ";
    return {.min = prefix + mName + suffix + "min",
            .max = prefix + mName + suffix + "max",
            .consistent = prefix + mName + suffix + "consistent",
            .average = prefix + mName + suffix + "average"};
}

void LayerInfo::RefreshRateHistory::clear() {
    mRefreshRates.clear();
}

bool LayerInfo::RefreshRateHistory::add(Fps refreshRate, nsecs_t now) {
    mRefreshRates.push_back({refreshRate, now});
    while (mRefreshRates.size() >= HISTORY_SIZE ||
           now - mRefreshRates.front().timestamp > HISTORY_DURATION.count()) {
        mRefreshRates.pop_front();
    }

    if (CC_UNLIKELY(sTraceEnabled)) {
        if (!mHeuristicTraceTagData.has_value()) {
            mHeuristicTraceTagData = makeHeuristicTraceTagData();
        }

        ATRACE_INT(mHeuristicTraceTagData->average.c_str(), refreshRate.getIntValue());
    }
#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection() && isShowCDDetailLog()) {
        ALOGI("%s: %s fps=%.2f", __func__, mName.c_str(), refreshRate.getValue());
    }
#endif

    return isConsistent();
}

bool LayerInfo::RefreshRateHistory::isConsistent() const {
    if (mRefreshRates.empty()) return true;

    const auto [min, max] =
            std::minmax_element(mRefreshRates.begin(), mRefreshRates.end(),
                                [](const auto& lhs, const auto& rhs) {
                                    return isStrictlyLess(lhs.refreshRate, rhs.refreshRate);
                                });

    const bool consistent =
            max->refreshRate.getValue() - min->refreshRate.getValue() < MARGIN_CONSISTENT_FPS;

    if (CC_UNLIKELY(sTraceEnabled)) {
        if (!mHeuristicTraceTagData.has_value()) {
            mHeuristicTraceTagData = makeHeuristicTraceTagData();
        }

        ATRACE_INT(mHeuristicTraceTagData->max.c_str(), max->refreshRate.getIntValue());
        ATRACE_INT(mHeuristicTraceTagData->min.c_str(), min->refreshRate.getIntValue());
        ATRACE_INT(mHeuristicTraceTagData->consistent.c_str(), consistent);
    }
#ifdef MTK_SF_MSYNC_3
    if (isSupportContentDetection() && isShowCDDetailLog()) {
        ALOGI("%s: %s max=%.2f min=%.2f consistent=%d", __func__, mName.c_str(), max->refreshRate.getValue(),
                min->refreshRate.getValue(), consistent);
    }
#endif

    return consistent;
}

} // namespace android::scheduler

// TODO(b/129481165): remove the #pragma below and fix conversion issues
#pragma clang diagnostic pop // ignored "-Wextra"
