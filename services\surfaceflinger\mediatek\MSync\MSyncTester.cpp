/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2021. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */
#ifdef MTK_SF_MSYNC

//#define LOG_NDEBUG 0
#undef LOG_TAG
#define LOG_TAG "MSyncTester"
#define ATRACE_TAG ATRACE_TAG_GRAPHICS

#include "MSyncTester.h"

#include <binder/Parcel.h>
#include <cutils/properties.h>
#include <log/log.h>

#include "Layer.h"
#include "MSyncSfApi.h"
#include "SurfaceFlinger.h"

namespace android {

enum {
    MSYNC_2_0_ENABLE = 9526'101,
    MSYNC_2_0_UPDATE_TARGET = 9526'102,
    MSYNC_2_0_CLEAR_TARGET = 9526'103,
    MSYNC_2_0_SET_PARAM_TABLE = 9526'104,
    MSYNC_2_0_GET_PARAM_TABLE = 9526'105,
};

bool MSyncTester::mTriggered = false;

MSyncTester& MSyncTester::getInstance() {
    static MSyncTester gInstance;
    return gInstance;
}

MSyncTester::MSyncTester() {
}

bool MSyncTester::isMSyncTransact(uint32_t code) {
    switch (code) {
        case MSYNC_2_0_ENABLE:
        case MSYNC_2_0_UPDATE_TARGET:
        case MSYNC_2_0_CLEAR_TARGET:
            mTriggered = true;
            return true;
        case MSYNC_2_0_SET_PARAM_TABLE:
        case MSYNC_2_0_GET_PARAM_TABLE:
            return true;
        default:
            return false;
    }
}

bool MSyncTester::isTriggered() {
    return mTriggered;
}

status_t MSyncTester::onTransact(std::shared_ptr<MSyncSfApi>& msyncSfApi,
                                 uint32_t code, const Parcel& data,
                                 Parcel* reply, uint32_t /*flags*/) {
    if (!msyncSfApi) {
        ALOGE("msyncSfApi == nullptr");
        return UNKNOWN_TRANSACTION;
    }

    switch (code) {
        case MSYNC_2_0_ENABLE: {
            int n = data.readInt32();
            bool enable = n == 0 ? false : true;
            mMSyncEnable = enable;
            ALOGI("mMSyncEnable %d", mMSyncEnable);
            return NO_ERROR;
        }
        case MSYNC_2_0_UPDATE_TARGET: {
            char value[PROPERTY_VALUE_MAX];

            property_get("vendor.debug.sf.msync.target_name_1", value, "\0");
            std::string name1 = value;
            // adb shell setprop vendor.debug.sf.msync.target_name_2 '' (for empty)
            property_get("vendor.debug.sf.msync.target_name_2", value, "\0");
            std::string name2 = value;

            mTargetName = name1 + name2;
            std::size_t startPos = mTargetName.find('[') + 1;
            mPackageName = mTargetName.substr(startPos, mTargetName.find('/') - startPos);

            ALOGI("mTargetName: %s", mTargetName.c_str());
            ALOGI("mPackageName: %s", mPackageName.c_str());
            return NO_ERROR;
        }
        case MSYNC_2_0_CLEAR_TARGET: {
            mTargetName.clear();
            ALOGI("mTargetName clear");
            return NO_ERROR;
        }
        case MSYNC_2_0_SET_PARAM_TABLE: {
            msyncSfApi->setMSyncParamTable(data);
            return NO_ERROR;
        }
        case MSYNC_2_0_GET_PARAM_TABLE: {
            msyncSfApi->getMSyncParamTable(reply);
            return NO_ERROR;
        }
        default:
            return UNKNOWN_TRANSACTION;
    }
}

void MSyncTester::onMessageRefresh(SurfaceFlinger* sf, std::shared_ptr<MSyncSfApi>& msyncSfApi) {
    if (!msyncSfApi) {
        return;
    }

    msyncSfApi->setMSyncEnable(mMSyncEnable);

    if (mTargetName.size() == 0) {
        msyncSfApi->setTargetLayer(nullptr);
        mTargetLayer = nullptr;
    } else {
        bool alreadySet = false;
        if (mTargetLayer) {
            sf->mDrawingState.traverseInZOrder([&](Layer* layer) {
                if (layer == mTargetLayer && layer->isVisible() && isLayerNameMatch(layer, mTargetName)) {
                    alreadySet = true;
                }
            });
        }

        if (!alreadySet) {
            sf->mDrawingState.traverseInZOrder([&](Layer* layer) {
                if (!alreadySet && layer->isVisible() && isLayerNameMatch(layer, mTargetName)) {
                    mTargetLayer = layer;
                    msyncSfApi->setTargetLayer(mTargetLayer);
                    alreadySet = true;
                }
            });
        }

        if (!alreadySet) {
            msyncSfApi->setTargetLayer(nullptr);
            mTargetLayer = nullptr;
        } else {
            bool bHasOtherUI = false;
            sf->mDrawingState.traverseInZOrder([&](Layer* layer) {
                if (layer->isVisible()) {
                    //ALOGI("%s: layer_name=%s, type=%s", __func__, layer->getName().c_str(), layer->getType());
                    if (layer->getName().find("NavigationBar") != std::string::npos
                            || layer->getName().find("StatusBar") != std::string::npos) {
                        // Recent app always has 'NavigationBar' layer
                        //ALOGI("%s: has NavigationBar", __func__);
                        bHasOtherUI = true;
                    } else if (layer->getName().find("SurfaceView") != std::string::npos
                            && layer->getName().find(mPackageName) == std::string::npos) {
                        // Current target layer has PIP window
                        //ALOGI("%s: has PIP window", __func__);
                        bHasOtherUI = true;
                        //msyncSfApi->setTargetLayer(nullptr);
                        //mTargetLayer = nullptr;
                    }
                }
            });
            msyncSfApi->setHasOtherUI(bHasOtherUI);
        }
    }
}

bool MSyncTester::isLayerNameMatch(const Layer* layer, std::string& str) {
    // before # in layer name
    const std::string& subName = layer->getName().substr(0, layer->getName().rfind('#'));
    return str.size() == subName.size() && str.compare(subName) == 0;
}

}; // namespace android
#endif
