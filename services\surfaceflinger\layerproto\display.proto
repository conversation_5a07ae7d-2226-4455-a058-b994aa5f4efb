/*
 * Copyright (C) 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";
option optimize_for = LITE_RUNTIME;

import "frameworks/native/services/surfaceflinger/layerproto/common.proto";

package android.surfaceflinger;

message DisplayProto {
    uint64 id = 1;

    string name = 2;

    uint32 layer_stack = 3;

    SizeProto size = 4;

    RectProto layer_stack_space_rect = 5;

    TransformProto transform = 6;

    bool is_virtual = 7;
}
