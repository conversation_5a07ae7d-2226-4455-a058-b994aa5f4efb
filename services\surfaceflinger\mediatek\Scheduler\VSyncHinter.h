#ifdef MTK_VSYNC_HINT_SUPPORT
#pragma once

#include <utils/Timers.h>

namespace android {

class VsyncHintApi;

class VSyncHinter {
public:
    struct VSyncInfo {
        int mVSyncType;
    };

    static VSyncHinter& getInstance();

    ~VSyncHinter();

    void fillVSyncInfo(VSyncInfo& info, const char* name);
    void onDispSyncEvent(VSyncInfo& info, nsecs_t period);

private:
    VSyncHinter();

    void* mVSyncHintHandle;
    VsyncHintApi* mVSyncHint;
};

} // namespace android
#endif
