/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2021. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */

#pragma once

#include <android-base/thread_annotations.h>
#include <utils/StrongPointer.h>
#include <utils/Timers.h>
#include <utils/Vector.h>

#include <vector>

namespace android {

#ifdef MTK_SF_MSYNC
struct ComposerState;

class DisplayDevice;
class Fence;
class IBinder;
class Layer;
class MSyncController;
class Parcel;
class SurfaceFlinger;

class MSyncSfApi {
public:
    MSyncSfApi(const sp<SurfaceFlinger>& flinger, bool useList);
    ~MSyncSfApi();

    void dump(std::string& result) const;
    void reloadConfigThenDump(std::string& result);

    void registerLayer(const Layer* layer);
    void destroyLayer(const Layer* layer);

    void onMessageInvalidate();
    void onMessageRefresh(sp<const DisplayDevice> display,
                          const std::vector<sp<DisplayDevice>>& displays0);
    void setTransactionState(const Vector<ComposerState>& states);
    void requestInvalidate();
    void onActiveModeUpdated();
    void setHasPendingBuffer(const Layer* layer);

    bool isOn() const;

    void setMSyncEnable(bool enable);
    void setTargetLayer(const void* layerPtr);
    void setHasOtherUI(bool bHasOtherUI);
    void setPreviousPresentFence(const sp<Fence>& previousPresentFence);
    void setSFInProgress(bool bSFInProgress);

    void setMSyncParamTable(const Parcel& data);
    void getMSyncParamTable(Parcel* reply);

private:
    // need sync with MSyncController.h
    using FuncGetLayerPtrFromhandle = std::function<void*(const sp<IBinder>&)>;
    using FuncTraverseVisibleLayers = std::function<void(std::function<void(const void*)>)>;
    using FuncTraverseVisibleBufferLayers = std::function<void(std::function<void(const void*, float, bool)>)>;
    using FuncGetCurrentVsyncPeriod = std::function<nsecs_t()>;
    using FuncTriggerInvalidate = std::function<void()>;
    using FuncTriggerEventQueueInvalidate = std::function<void()>;
    using FuncSetHwcMSyncOn = std::function<void()>;
    using FuncSetHwcMSyncOff = std::function<void()>;

    // need sync with MSyncExportApi.h
    typedef MSyncController* (*FuncCreateMSyncController)(bool useList,
                                                               FuncGetLayerPtrFromhandle,
                                                               FuncTraverseVisibleLayers,
                                                               FuncTraverseVisibleBufferLayers,
                                                               FuncGetCurrentVsyncPeriod,
                                                               FuncTriggerInvalidate,
                                                               FuncTriggerEventQueueInvalidate,
                                                               FuncSetHwcMSyncOn,
                                                               FuncSetHwcMSyncOff);
    typedef void (*FuncDestroyMSyncController)(MSyncController* controller);
    typedef void (*FuncDump)(MSyncController* controller, std::string& result);
    typedef void (*FuncReloadConfigThenDump)(MSyncController* controller, std::string& result);

    typedef void (*FuncRegisterLayer)(MSyncController* controller, const void* layerPtr, const std::string& name);
    typedef void (*FuncDestroyLayer)(MSyncController* controller, const void* layerPtr);

    typedef void (*FuncOnMessageInvalidate)(MSyncController* controller);
    typedef void (*FuncOnMessageRefresh)(MSyncController* controller, bool hasExternalDisp, bool hasGpuVirtualDisp, uint32_t primaryDisplayArea);
    typedef void (*FuncSetTransactionState)(MSyncController* controller,
                                            const std::vector<std::tuple<sp<IBinder>, bool, bool, sp<Fence>>>& states);
    typedef void (*FuncRequestInvalidate)(MSyncController* controller);
    typedef void (*FuncOnActiveModeUpdated)(MSyncController* controller);
    typedef void (*FuncSetHasPendingBuffer)(MSyncController* controller, const void* layerPtr);

    typedef bool (*FuncIsOn)(MSyncController* controller);

    typedef void (*FuncSetMSyncEnable)(MSyncController* controller, bool enable);
    typedef void (*FuncSetTargetLayer)(MSyncController* controller, const void* layerPtr);
    typedef void (*FuncSetHasOtherUI)(MSyncController* controller, bool bHasOtherUI);
    typedef void (*FuncSetPreviousPresentFence)(MSyncController* controller, const sp<Fence>& previousPresentFence);
    typedef void (*FuncSetSFInProgress)(MSyncController* controller, bool bSFInProgress);

private:
    void* mSoHandle = nullptr;

    sp<SurfaceFlinger> mFlinger /*= nullptr*/;
    MSyncController* mMSyncController = nullptr;

    // dlsym function pointer
    int loadMSyncApi();
    FuncCreateMSyncController mFuncCreateMSyncController = nullptr;
    FuncDestroyMSyncController mFuncDestroyMSyncController = nullptr;
    FuncDump mFuncDump = nullptr;
    FuncReloadConfigThenDump mFuncReloadConfigThenDump = nullptr;
    FuncRegisterLayer mFuncRegisterLayer = nullptr;
    FuncDestroyLayer mFuncDestroyLayer = nullptr;
    FuncOnMessageInvalidate mFuncOnMessageInvalidate = nullptr;
    FuncOnMessageRefresh mFuncOnMessageRefresh = nullptr;
    FuncSetTransactionState mFuncSetTransactionState = nullptr;
    FuncRequestInvalidate mFuncRequestInvalidate = nullptr;
    FuncOnActiveModeUpdated mFuncOnActiveModeUpdated = nullptr;
    FuncSetHasPendingBuffer mFuncSetHasPendingBuffer = nullptr;
    FuncIsOn mFuncIsOn = nullptr;
    FuncSetMSyncEnable mFuncSetMSyncEnable = nullptr;
    FuncSetTargetLayer mFuncSetTargetLayer = nullptr;
    FuncSetHasOtherUI mFuncSetHasOtherUI = nullptr;
    FuncSetPreviousPresentFence mFuncSetPreviousPresentFence = nullptr;
    FuncSetSFInProgress mFuncSetSFInProgress = nullptr;
};
#endif

}; // namespace android
