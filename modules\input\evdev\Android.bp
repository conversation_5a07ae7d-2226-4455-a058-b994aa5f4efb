// Copyright (C) 2015 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Evdev module implementation
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "hardware_libhardware_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["hardware_libhardware_license"],
}

cc_library_shared {
    name: "libinput_evdev",

    srcs: [
        "BitUtils.cpp",
        "InputHub.cpp",
        "InputDevice.cpp",
        "InputDeviceManager.cpp",
        "InputHost.cpp",
        "InputMapper.cpp",
        "MouseInputMapper.cpp",
        "SwitchInputMapper.cpp",
    ],

    header_libs: ["jni_headers"],
    shared_libs: [
        "libhardware_legacy",
        "liblog",
        "libutils",
    ],

    export_include_dirs: ["."],

    cppflags: [
        "-Wno-unused-parameter",
    ],
}

// HAL module
cc_library_shared {
    name: "input.evdev.default",
    relative_install_path: "hw",

    srcs: ["EvdevModule.cpp"],

    shared_libs: [
        "libinput_evdev",
        "liblog",
    ],

    cppflags: [
        "-Wall",
        "-Werror",
        "-Wno-unused-parameter",
    ],
}
