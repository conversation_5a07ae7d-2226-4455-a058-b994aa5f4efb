#include "mediatek/DispDeJitterHelper.h"

#include <dlfcn.h>
#include <cutils/log.h>

namespace android {

DispDeJitterHelper& DispDeJitterHelper::getInstance() {
    static DispDeJitterHelper gInstance;
    return gInstance;
}

DispDeJitterHelper::DispDeJitterHelper()
      : mSo<PERSON><PERSON>le(nullptr),
        mFnCreateDispDeJitter(nullptr),
        mFnDestroyDispDeJitter(nullptr),
        mFnShouldDelayPresent(nullptr),
        mFnMarkTimestamp(nullptr) {
    typedef DispDeJitter* (*CreateDispDeJitter)();
    typedef void (*DestroyDispDeJitter)(DispDeJitter*);
    typedef bool (*ShouldDelayPresent)(DispDeJitter*, const std::string&, const sp<GraphicBuffer>&, const nsecs_t&,
        const int, const int64_t, const int64_t);
    typedef void (*MarkTimestamp)(const sp<GraphicBuffer>&, const uint64_t);

    // dlopen must set RTLD_LAZY flag because of performance issue
    mSoHandle = dlopen("libdisp_dejitter.so", RTLD_LAZY);
    if (mSoHandle) {
        mFnCreateDispDeJitter = reinterpret_cast<CreateDispDeJitter>(dlsym(mSoHandle, "createDispDeJitter"));
        mFnDestroyDispDeJitter = reinterpret_cast<DestroyDispDeJitter>(dlsym(mSoHandle, "destroyDispDeJitter"));
        mFnShouldDelayPresent = reinterpret_cast<ShouldDelayPresent>(dlsym(mSoHandle, "shouldDelayPresent"));
        mFnMarkTimestamp = reinterpret_cast<MarkTimestamp>(dlsym(mSoHandle, "markTimestamp"));
        if (nullptr == mFnCreateDispDeJitter) {
            ALOGE("finding createDispDeJitter() failed");
        }
        if (nullptr == mFnDestroyDispDeJitter) {
            ALOGE("finding destroyDispDeJitter() failed");
        }
        if (nullptr == mFnShouldDelayPresent) {
            ALOGE("finding shouldDelayPresent() failed");
        }
        if (nullptr == mFnMarkTimestamp) {
            ALOGE("finding markTimestamp() failed");
        }
    } else {
        ALOGE("open libdisp_dejitter failed");
    }
}

DispDeJitterHelper::~DispDeJitterHelper() {
    if (mSoHandle) {
        dlclose(mSoHandle);
    }
}

DispDeJitter* DispDeJitterHelper::createDispDeJitter() {
    if (mFnCreateDispDeJitter) {
        return mFnCreateDispDeJitter();
    }
    return nullptr;
}

void DispDeJitterHelper::destroyDispDeJitter(DispDeJitter* dispDeJitter) {
    if (mFnDestroyDispDeJitter) {
        mFnDestroyDispDeJitter(dispDeJitter);
    }
}

bool DispDeJitterHelper::shouldPresentNow(
    DispDeJitter* dispDeJitter,
    const std::string& name,
    const sp<GraphicBuffer>& gb,
    const nsecs_t& expectedPresent,
    const bool isDue,
    const int pendingBufferCount,
    const int64_t cameraFps,
    const int64_t cameraPbc) {
    if (dispDeJitter == nullptr)
        return isDue;

    bool needDelay = false;
    if (mFnShouldDelayPresent) {
        needDelay = mFnShouldDelayPresent(dispDeJitter, name, gb, expectedPresent, pendingBufferCount, cameraFps, cameraPbc);
    }

    return (!needDelay && isDue);
}

void DispDeJitterHelper::markTimestamp(const sp<GraphicBuffer>& gb, const uint64_t q_time) {
    if (mFnMarkTimestamp) {
        mFnMarkTimestamp(gb, q_time);
    }
}

} // namespace android
