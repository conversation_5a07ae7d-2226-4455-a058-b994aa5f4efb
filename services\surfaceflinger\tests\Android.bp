// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_native_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_native_license"],
}

cc_test {
    name: "SurfaceFlinger_test",
    defaults: ["surfaceflinger_defaults"],
    test_suites: ["device-tests"],
    srcs: [
        "BootDisplayMode_test.cpp",
        "BufferGenerator.cpp",
        "CommonTypes_test.cpp",
        "Credentials_test.cpp",
        "DereferenceSurfaceControl_test.cpp",
        "DisplayConfigs_test.cpp",
        "DisplayEventReceiver_test.cpp",
        "EffectLayer_test.cpp",
        "InvalidHandles_test.cpp",
        "LayerCallback_test.cpp",
        "LayerRenderTypeTransaction_test.cpp",
        "LayerState_test.cpp",
        "LayerTransaction_test.cpp",
        "LayerTypeAndRenderTypeTransaction_test.cpp",
        "LayerTypeTransaction_test.cpp",
        "LayerUpdate_test.cpp",
        "MirrorLayer_test.cpp",
        "MultiDisplayLayerBounds_test.cpp",
        "RefreshRateOverlay_test.cpp",
        "RelativeZ_test.cpp",
        "ReleaseBufferCallback_test.cpp",
        "ScreenCapture_test.cpp",
        "SetFrameRate_test.cpp",
        "SetFrameRateOverride_test.cpp",
        "SetGeometry_test.cpp",
        "Stress_test.cpp",
        "SurfaceInterceptor_test.cpp",
        "VirtualDisplay_test.cpp",
        "WindowInfosListener_test.cpp",
    ],
    data: ["SurfaceFlinger_test.filter"],
    static_libs: [
        "libtrace_proto",
        "liblayers_proto",
        "android.hardware.graphics.composer@2.1",
    ],
    shared_libs: [
        "android.hardware.graphics.common-V3-ndk",
        "android.hardware.graphics.common@1.2",
        "libandroid",
        "libbase",
        "libbinder",
        "libcutils",
        "libEGL",
        "libGLESv2",
        "libgui",
        "liblog",
        "libnativewindow",
        "libprotobuf-cpp-full",
        "libui",
        "libutils",
    ],
    header_libs: [
        "libnativewindow_headers",
    ],
}

cc_defaults {
    name: "ipc_defaults",
    cflags: [
        "-Wall",
        "-Werror",
    ],
}

cc_test {
    name: "IPC_test",
    defaults: ["ipc_defaults"],
    test_suites: ["device-tests"],
    srcs: [
        "BufferGenerator.cpp",
        "IPC_test.cpp",
    ],
    cppflags: [
        "-Wall",
        "-Werror",
        "-Wformat",
        "-Wthread-safety",
        "-Wunused",
        "-Wunreachable-code",
    ],
    shared_libs: [
        "libandroid",
        "libbinder",
        "libcutils",
        "libEGL",
        "libGLESv2",
        "libgui",
        "liblayers_proto",
        "liblog",
        "libprotobuf-cpp-full",
        "libui",
        "libutils",
    ],
    cpp_std: "experimental",
    gnu_extensions: false,
}

subdirs = [
    "fakehwc",
    "hwc2",
    "unittests",
    "utils",
    "vsync",
    "waitforvsync",
]
