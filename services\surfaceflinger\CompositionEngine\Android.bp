package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_native_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_native_license"],
}

cc_defaults {
    name: "libcompositionengine_defaults",
    defaults: ["surfaceflinger_defaults"],
    cflags: [
        "-DLOG_TAG=\"CompositionEngine\"",
        "-DATRACE_TAG=ATRACE_TAG_GRAPHICS",

        // MTK AOSP enhancement required
        "-DMTK_SF_DEBUG_SUPPORT",
        // MTK AOSP enhancement required
        "-DMTK_SF_PQ_MANAGEMENT",
        // for smart composition feature
        "-DMTK_SF_SMART_COMPOSITION",
        // MTK AOSP enhancement required
        "-DMTK_SF_HWC_REPAINT_SUPPORT",
    ],
    shared_libs: [
        "android.hardware.graphics.allocator@2.0",
        "android.hardware.graphics.composer@2.1",
        "android.hardware.graphics.composer@2.2",
        "android.hardware.graphics.composer@2.3",
        "android.hardware.graphics.composer@2.4",
        "android.hardware.graphics.composer3-V1-ndk",
        "android.hardware.power@1.0",
        "android.hardware.power@1.3",
        "android.hardware.power-V2-cpp",
        "libbase",
        "libcutils",
        "libgui",
        "liblayers_proto",
        "liblog",
        "libnativewindow",
        "libprotobuf-cpp-lite",
        "libSurfaceFlingerProp",
        "libtimestats",
        "libui",
        "libutils",
        "libgralloc_extra_sys",
    ],
    static_libs: [
        "libmath",
        "librenderengine",
        "libtonemap",
        "libtrace_proto",
        "libaidlcommonsupport",
        // MTK AOSP enhancement required
        "libsf_mtk_property",
        "libprocessgroup",
        "libcgrouprc",
        "libjsoncpp",
        "libcgrouprc_format",
    ],
    header_libs: [
        "android.hardware.graphics.composer@2.1-command-buffer",
        "android.hardware.graphics.composer@2.2-command-buffer",
        "android.hardware.graphics.composer@2.3-command-buffer",
        "android.hardware.graphics.composer@2.4-command-buffer",
        "android.hardware.graphics.composer3-command-buffer",
        "libsurfaceflinger_headers",
        "libgralloc_metadata_sys_headers",
    ],
}

cc_library {
    name: "libcompositionengine",
    defaults: ["libcompositionengine_defaults"],
    srcs: [
        "src/planner/CachedSet.cpp",
        "src/planner/Flattener.cpp",
        "src/planner/LayerState.cpp",
        "src/planner/Planner.cpp",
        "src/planner/Predictor.cpp",
        "src/planner/TexturePool.cpp",
        "src/ClientCompositionRequestCache.cpp",
        "src/CompositionEngine.cpp",
        "src/Display.cpp",
        "src/DisplayColorProfile.cpp",
        "src/DisplaySurface.cpp",
        "src/DumpHelpers.cpp",
        "src/HwcAsyncWorker.cpp",
        "src/HwcBufferCache.cpp",
        "src/LayerFECompositionState.cpp",
        "src/Output.cpp",
        "src/OutputCompositionState.cpp",
        "src/OutputLayer.cpp",
        "src/OutputLayerCompositionState.cpp",
        "src/RenderSurface.cpp",

        // MTK AOSP enhancement required
        "mediatek/MtkCeDebug.cpp",
    ],
    local_include_dirs: ["include"],
    export_include_dirs: ["include"],
}

cc_library {
    name: "libcompositionengine_mocks",
    defaults: ["libcompositionengine_defaults"],
    srcs: [
        "mock/CompositionEngine.cpp",
        "mock/Display.cpp",
        "mock/DisplayColorProfile.cpp",
        "mock/DisplaySurface.cpp",
        "mock/LayerFE.cpp",
        "mock/NativeWindow.cpp",
        "mock/Output.cpp",
        "mock/OutputLayer.cpp",
        "mock/RenderSurface.cpp",
    ],
    static_libs: [
        "libgtest",
        "libgmock",
        "libcompositionengine",
    ],
    local_include_dirs: ["include"],
    export_include_dirs: ["include"],
}

cc_test {
    name: "libcompositionengine_test",
    test_suites: ["device-tests"],
    defaults: ["libcompositionengine_defaults"],
    srcs: [
        "tests/planner/CachedSetTest.cpp",
        "tests/planner/FlattenerTest.cpp",
        "tests/planner/LayerStateTest.cpp",
        "tests/planner/PredictorTest.cpp",
        "tests/planner/TexturePoolTest.cpp",
        "tests/CompositionEngineTest.cpp",
        "tests/DisplayColorProfileTest.cpp",
        "tests/DisplayTest.cpp",
        "tests/HwcBufferCacheTest.cpp",
        "tests/MockHWC2.cpp",
        "tests/MockHWComposer.cpp",
        "tests/MockPowerAdvisor.cpp",
        "tests/OutputLayerTest.cpp",
        "tests/OutputTest.cpp",
        "tests/ProjectionSpaceTest.cpp",
        "tests/RenderSurfaceTest.cpp",
    ],
    static_libs: [
        "libcompositionengine",
        "libcompositionengine_mocks",
        "libgui_mocks",
        "librenderengine_mocks",
        "libgmock",
        "libgtest",
    ],
    sanitize: {
        // By using the address sanitizer, we not only uncover any issues
        // with the test, but also any issues with the code under test.
        //
        // Note: If you get an runtime link error like:
        //
        //   CANNOT LINK EXECUTABLE "/data/local/tmp/libcompositionengine_test": library "libclang_rt.asan-aarch64-android.so" not found
        //
        // it is because the address sanitizer shared objects are not installed
        // by default in the system image.
        //
        // You can either "make dist tests" before flashing, or set this
        // option to false temporarily.
        address: true,
    },
}
