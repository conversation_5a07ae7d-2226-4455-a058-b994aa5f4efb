#ifdef MTK_SF_PERF_API
#include "SFPerfAPILoader.h"

#include <cinttypes>
#include <cstring>
#include <dlfcn.h>
#include <log/log.h>
#include <errno.h>

#include "SFPerfAPILoader.h"

namespace android {

#undef LOG_TAG
#define LOG_TAG "SFPerfAPILoader"

SFPerfAPILoader& SFPerfAPILoader::getInstance() {
    static SFPerfAPILoader gInstance;
    return gInstance;
}

SFPerfAPILoader::SFPerfAPILoader():
        m_so_handle(nullptr),
        mPerfHelper(nullptr),
        mFnDestroyPerfHelper(nullptr),
        mFnSetUclampMin(nullptr),
        mFnSetTaskUclamp(nullptr),
        mFnAddThreadtoCgroup(nullptr)
        {
    m_so_handle = dlopen("libsf_perf.so", RTLD_LAZY);
    if (m_so_handle) {
        createPerfHelperFn createPtr = reinterpret_cast<createPerfHelperFn>(dlsym(m_so_handle, "createPerfHelper"));
        if (createPtr) {
            mPerfHelper = createPtr();
            if (!mPerfHelper) {
                ALOGE("Failed to create PerfHelper");
            } else {
                ALOGV("SFPerfAPILoader created PerfHelper");
            }
        } else {
            ALOGE("Failed to get function: createPerfHelper");
        }
        mFnDestroyPerfHelper = reinterpret_cast<DestroyPerfHelperFn>(dlsym(m_so_handle, "destroyPerfHelper"));
        if (!mFnDestroyPerfHelper) {
            ALOGE("Failed to get function: destroyPerfHelper");
        }
        mFnSetUclampMin = reinterpret_cast<SetUclampMinFn>(dlsym(m_so_handle, "setUclampMin"));
        if (mFnSetUclampMin == nullptr) {
            ALOGE("Failed to get function: setUclampMin");
        } else {
            ALOGV("set mFnSetUclampMin %p", mFnSetUclampMin);
        }
        mFnSetTaskUclamp = reinterpret_cast<SetTaskUclampFn>(dlsym(m_so_handle, "setTaskUclamp"));
        if (mFnSetTaskUclamp == nullptr) {
            ALOGE("Failed to get function: setTaskUclamp");
        } else {
            ALOGV("set mFnSetTaskUclamp %p", mFnSetTaskUclamp);
        }
        mFnAddThreadtoCgroup = reinterpret_cast<AddThreadtoCgroupFn>(dlsym(m_so_handle, "addThreadtoCgroup"));
        if (mFnAddThreadtoCgroup == nullptr) {
            ALOGE("Failed to get function: addThreadtoCgroup");
        } else {
            ALOGV("set mFnAddThreadtoCgroup %p", mFnAddThreadtoCgroup);
        }
    } else {
        ALOGE("Failed to load libsf_perf.so");
    }
}

SFPerfAPILoader::~SFPerfAPILoader() {
    if (mPerfHelper && mFnDestroyPerfHelper) {
        mFnDestroyPerfHelper(mPerfHelper);
        mPerfHelper = NULL;
        mFnDestroyPerfHelper = NULL;
    }
    if (m_so_handle) {
        dlclose(m_so_handle);
        m_so_handle = NULL;
    }
}

int SFPerfAPILoader::perfUclampMinRequest(pid_t pid, int uClampMin) {
    if (!mPerfHelper) {
        ALOGE("perfUclampMinRequest mPerfHelper NULL");
        errno = EINVAL;
        return -1;
    }
    if (mFnSetUclampMin == nullptr) {
        ALOGE("perfUclampMinRequest mFnSetUclampMin NULL");
        errno = EINVAL;
        return -1;
    }
    ALOGI("perfUclampMinRequest uclamp min %d", uClampMin);
    return mFnSetUclampMin(mPerfHelper, pid, (uClampMin*100)/1024);
}

int SFPerfAPILoader::perfTaskUclampRequest(int uClampMin, int uClampMax) {
    if (!mPerfHelper) {
        ALOGE("perfTaskUclampRequest mPerfHelper NULL");
        errno = EINVAL;
        return -1;
    }
    if (mFnSetTaskUclamp == nullptr) {
        ALOGE("perfTaskUclampRequest mFnSetTaskUclamp NULL");
        errno = EINVAL;
        return -1;
    }
    ALOGV("perfTaskUclampRequest uclamp min %d, uclamp max %d", uClampMin, uClampMax);
    return mFnSetTaskUclamp(mPerfHelper, (uClampMin*100)/1024, (uClampMax*100)/1024);
}

int SFPerfAPILoader::perfAddThreadRequest(pid_t pid) {
    if (!mPerfHelper) {
        ALOGE("perfAddThreadRequest mPerfHelper NULL");
        errno = EINVAL;
        return -1;
    }
    if (mFnAddThreadtoCgroup == nullptr) {
        ALOGE("perfAddPidtoCgroup mFnAddThreadtoCgroup NULL");
        errno = EINVAL;
        return -1;
    }
    return mFnAddThreadtoCgroup(mPerfHelper, pid);
}
} // namespace android
#endif
