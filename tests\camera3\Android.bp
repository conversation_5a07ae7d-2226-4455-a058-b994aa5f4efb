package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "hardware_libhardware_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["hardware_libhardware_license"],
}

cc_test {
    name: "camera3_tests",
    srcs: ["camera3tests.cpp"],

    shared_libs: [
        "liblog",
        "libhardware",
        "libcamera_metadata",
    ],

    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
    ],
}
