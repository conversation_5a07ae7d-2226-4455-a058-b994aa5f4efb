#ifndef __ANDROID_DISPDEJITTER_HELPER_H__
#define __ANDROID_DISPDEJITTER_HELPER_H__

#include <utils/Timers.h>
#include <utils/RefBase.h>

namespace android {

class DispDeJitter;
class GraphicBuffer;

class DispDeJitterHelper {
public:
    static DispDeJitterHelper& getInstance();

    virtual ~DispDeJitterHelper();

    DispDeJitter* createDispDeJitter();
    void destroyDispDeJitter(DispDeJitter* dispDeJitter);
    bool shouldPresentNow(DispDeJitter* dispDeJitter, const std::string& name, const sp<GraphicBuffer>& gb,
                          const nsecs_t& expectedPresent, const bool isDue, const int pendingBufferCount,
                          const int64_t cameraFps, const int64_t cameraPbc);
    void markTimestamp(const sp<GraphicBuffer>& gb, const uint64_t q_time);

protected:
    DispDeJitterHelper();

    void* mSoHandle;
    DispDeJitter* (*mFnCreateDispDeJitter)();
    void (*mFnDestroyDispDeJitter)(DispDeJitter*);
    bool (*mFnShouldDelayPresent)(DispDeJitter*, const std::string&, const sp<GraphicBuffer>&, const nsecs_t&, const int,
        const int64_t, const int64_t);
    void (*mFnMarkTimestamp)(const sp<GraphicBuffer>&, const uint64_t);
};

}   // namespace android
#endif  // __ANDROID_DISPDEJITTER_HELPER_H__

