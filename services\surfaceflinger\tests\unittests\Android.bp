// Copyright 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_native_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_native_license"],
}

filegroup {
    name: "libsurfaceflinger_mock_sources",
    srcs: [
        "mock/DisplayHardware/MockComposer.cpp",
        "mock/DisplayHardware/MockHWC2.cpp",
        "mock/DisplayHardware/MockIPower.cpp",
        "mock/DisplayHardware/MockIPowerHintSession.cpp",
        "mock/DisplayHardware/MockPowerAdvisor.cpp",
        "mock/MockEventThread.cpp",
        "mock/MockFrameTimeline.cpp",
        "mock/MockFrameTracer.cpp",
        "mock/MockNativeWindowSurface.cpp",
        "mock/MockSurfaceInterceptor.cpp",
        "mock/MockTimeStats.cpp",
        "mock/MockVsyncController.cpp",
        "mock/MockVSyncTracker.cpp",
        "mock/system/window/MockNativeWindow.cpp",
    ],
}

cc_test {
    name: "libsurfaceflinger_unittest",
    defaults: [
        "libsurfaceflinger_mocks_defaults",
        "skia_renderengine_deps",
        "surfaceflinger_defaults",
    ],
    test_suites: ["device-tests"],
    sanitize: {
        // Using the address sanitizer not only helps uncover issues in the code
        // covered by the tests, but also covers some of the tricky injection of
        // fakes the unit tests currently do.
        //
        // Note: If you get an runtime link error like:
        //
        //   CANNOT LINK EXECUTABLE "/data/local/tmp/libsurfaceflinger_unittest": library "libclang_rt.asan-aarch64-android.so" not found
        //
        // it is because the address sanitizer shared objects are not installed
        // by default in the system image.
        //
        // You can either "make dist tests" before flashing, or set this
        // option to false temporarily.
        address: true,
    },
    srcs: [
        ":libsurfaceflinger_mock_sources",
        ":libsurfaceflinger_sources",
        "libsurfaceflinger_unittest_main.cpp",
        "AidlPowerHalWrapperTest.cpp",
        "CachingTest.cpp",
        "CompositionTest.cpp",
        "DispSyncSourceTest.cpp",
        "DisplayIdGeneratorTest.cpp",
        "DisplayTransactionTest.cpp",
        "DisplayDevice_GetBestColorModeTest.cpp",
        "DisplayDevice_InitiateModeChange.cpp",
        "DisplayDevice_SetDisplayBrightnessTest.cpp",
        "DisplayDevice_SetProjectionTest.cpp",
        "EventThreadTest.cpp",
        "FlagManagerTest.cpp",
        "FpsReporterTest.cpp",
        "FpsTest.cpp",
        "FramebufferSurfaceTest.cpp",
        "FrameRateOverrideMappingsTest.cpp",
        "FrameTimelineTest.cpp",
        "GameModeTest.cpp",
        "HWComposerTest.cpp",
        "OneShotTimerTest.cpp",
        "LayerHistoryTest.cpp",
        "LayerInfoTest.cpp",
        "LayerMetadataTest.cpp",
        "LayerTest.cpp",
        "LayerTestUtils.cpp",
        "MessageQueueTest.cpp",
        "SurfaceFlinger_CreateDisplayTest.cpp",
        "SurfaceFlinger_DestroyDisplayTest.cpp",
        "SurfaceFlinger_DisplayModeSwitching.cpp",
        "SurfaceFlinger_DisplayTransactionCommitTest.cpp",
        "SurfaceFlinger_GetDisplayNativePrimariesTest.cpp",
        "SurfaceFlinger_HotplugTest.cpp",
        "SurfaceFlinger_NotifyPowerBoostTest.cpp",
        "SurfaceFlinger_OnInitializeDisplaysTest.cpp",
        "SurfaceFlinger_PowerHintTest.cpp",
        "SurfaceFlinger_SetDisplayStateTest.cpp",
        "SurfaceFlinger_SetPowerModeInternalTest.cpp",
        "SurfaceFlinger_SetupNewDisplayDeviceInternalTest.cpp",
        "SchedulerTest.cpp",
        "SetFrameRateTest.cpp",
        "RefreshRateConfigsTest.cpp",
        "RefreshRateSelectionTest.cpp",
        "RefreshRateStatsTest.cpp",
        "RegionSamplingTest.cpp",
        "TimeStatsTest.cpp",
        "FrameTracerTest.cpp",
        "TransactionApplicationTest.cpp",
        "TransactionFrameTracerTest.cpp",
        "TransactionProtoParserTest.cpp",
        "TransactionSurfaceFrameTest.cpp",
        "TransactionTracingTest.cpp",
        "TunnelModeEnabledReporterTest.cpp",
        "StrongTypingTest.cpp",
        "VSyncDispatchTimerQueueTest.cpp",
        "VSyncDispatchRealtimeTest.cpp",
        "VsyncModulatorTest.cpp",
        "VSyncPredictorTest.cpp",
        "VSyncReactorTest.cpp",
        "VsyncConfigurationTest.cpp",
    ],
}

cc_defaults {
    name: "libsurfaceflinger_mocks_defaults",
    static_libs: [
        "android.hardware.common-V2-ndk",
        "android.hardware.common.fmq-V1-ndk",
        "android.hardware.graphics.common-V3-ndk",
        "android.hardware.graphics.composer@2.1",
        "android.hardware.graphics.composer@2.2",
        "android.hardware.graphics.composer@2.3",
        "android.hardware.graphics.composer@2.4",
        "android.hardware.graphics.composer3-V1-ndk",
        "android.hardware.power@1.0",
        "android.hardware.power@1.1",
        "android.hardware.power@1.2",
        "android.hardware.power@1.3",
        "android.hardware.power-V2-cpp",
        "libaidlcommonsupport",
        "libcompositionengine_mocks",
        "libcompositionengine",
        "libframetimeline",
        "libgmock",
        "libgui_mocks",
        "liblayers_proto",
        "libperfetto_client_experimental",
        "librenderengine",
        "librenderengine_mocks",
        "libscheduler",
        "libserviceutils",
        "libtimestats",
        "libtimestats_atoms_proto",
        "libtimestats_proto",
        "libtonemap",
        "libtrace_proto",
        "perfetto_trace_protos",

        // MTK AOSP enhancement required
        "libsf_mtk_property",
    ],
    shared_libs: [
        "android.hardware.configstore-utils",
        "android.hardware.configstore@1.0",
        "android.hardware.configstore@1.1",
        "android.hardware.graphics.allocator@2.0",
        "android.hardware.graphics.allocator@3.0",
        "android.hardware.graphics.common@1.2",
        "libbase",
        "libbinder",
        "libbinder_ndk",
        "libcutils",
        "libEGL",
        "libfmq",
        "libGLESv1_CM",
        "libGLESv2",
        "libgui",
        "libhidlbase",
        "libinput",
        "liblog",
        "libnativewindow",
        "libprocessgroup",
        "libprotobuf-cpp-lite",
        "libSurfaceFlingerProp",
        "libsync",
        "libui",
        "libutils",
        "server_configurable_flags",
    ],
    header_libs: [
        "android.hardware.graphics.composer3-command-buffer",
        "android.hardware.graphics.composer@2.1-command-buffer",
        "android.hardware.graphics.composer@2.2-command-buffer",
        "android.hardware.graphics.composer@2.3-command-buffer",
        "android.hardware.graphics.composer@2.4-command-buffer",
        "libscheduler_test_headers",
        "libsurfaceflinger_headers",
    ],
}

cc_library_headers {
    name: "libsurfaceflinger_mocks_headers",
    export_include_dirs: ["."],
}
