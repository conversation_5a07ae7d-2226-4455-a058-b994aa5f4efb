/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2018. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */
// TODO(b/129481165): remove the #pragma below and fix conversion issues
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wconversion"

#include "MtkDebugAPI.h"

#include <chrono>
#include <sstream>
#include <thread>

#include <cutils/compiler.h>  // For CC_[UN]LIKELY
#include <layerproto/LayerProtoParser.h>
#include <log/log.h>
#include <ui/GraphicBuffer.h>

#include "mediatek/SFProperty.h"
#include "mediatek/PropertiesState.h"
#include "mediatek/SFDebugAPILoader.h"

#include "Layer.h"
#include "Tracing/LayerTracing.h"

namespace android {

#ifdef MTK_SF_DEBUG_SUPPORT
void slowMotion() {
    // to slow down FPS
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mDelayTime !=
            std::chrono::milliseconds::zero())) {
        std::ostringstream ss;
        ss << SFProperty::getInstance().getPropState()->mDelayTime.count();
        ALOGI("SurfaceFlinger slow motion timer: %s ms", ss.str().c_str());
        std::this_thread::sleep_for(SFProperty::getInstance().getPropState()->mDelayTime);
    }
}

void logTransaction(Layer* layer)
{
    // dump state result after transaction committed
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mLogTransaction)) {
        LayersProto layersProto;
        layer->writeToProto(layersProto, LayerTracing::TRACE_ALL);
        auto layerTree = LayerProtoParser::generateLayerTree(layersProto);
        std::string str = LayerProtoParser::layerTreeToString(std::move(layerTree));
        std::string::size_type pos1, pos2;
        pos2 = str.find("\n");
        pos1 = 0;
        while (std::string::npos != pos2) {
            ALOGD("%s", str.substr(pos1, pos2-pos1).c_str());
            pos1 = pos2 + std::strlen("\n");
            pos2 = str.find("\n", pos1);
        }
        if (pos1 != str.length()){
            ALOGD("%s", str.substr(pos1).c_str());
        }
    }
}

void dumpScreenShot(const sp<GraphicBuffer>& outBuffer)
{
    if (CC_UNLIKELY(SFProperty::getInstance().getPropState()->mDumpScreenShot > 0)) {
        String8 s = String8::format("captureScreen_%08d",
                SFProperty::getInstance().getPropState()->mDumpScreenShot);
        if (outBuffer != nullptr)
        {
            if ((outBuffer)->handle != nullptr)
            {
                SFDebugAPI::getInstance().dumpBuffer((outBuffer)->handle, s.string());
            }
        }
        SFProperty::getInstance().getPropState()->mDumpScreenShot++;
    }
}
#endif

}; // namespace android
