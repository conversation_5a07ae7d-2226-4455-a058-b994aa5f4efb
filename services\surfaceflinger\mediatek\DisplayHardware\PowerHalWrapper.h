#ifndef __ANDROID_POWERHAL_HELPER_H__
#define __ANDROID_POWERHAL_HELPER_H__

#include <utils/Singleton.h>

#ifdef MTK_SF_HINT_DISPLAY_INFO

#define LIB_FULL_NAME "libpowerhalwrap.so"
typedef int (*enableMultiDisplayMode)(int32_t, int32_t);

namespace android {

class PowerHalWrapper {
public:
    PowerHalWrapper();
    ~PowerHalWrapper();
    void hintMultiDisplay(const int& enable, const int& fps);
    static PowerHalWrapper& getInstance();
private:
    void *mSoHandle;
    int (*mEnableMultiDisplayMode)(int32_t, int32_t);
};

}   // namespace android

#endif  // MTK_SF_HINT_DISPLAY_INFO
#endif  // __ANDROID_POWERHAL_HELPER_H__